from app_new import app, db, EducationalLevel, LevelDatabase, LevelDataEntry

def main():
    with app.app_context():
        # الحصول على جميع المستويات التعليمية
        levels = EducationalLevel.query.all()
        
        for level in levels:
            print(f"\n=== المستوى: {level.name} ===")
            
            # الحصول على قاعدة بيانات المستوى
            database = LevelDatabase.query.filter_by(level_id=level.id).first()
            
            if not database:
                print(f"خطأ: لا توجد قاعدة بيانات للمستوى {level.name}")
                continue
            
            # الحصول على المواد الدراسية
            subjects = LevelDataEntry.query.filter_by(database_id=database.id, entry_type='subject').all()
            print(f"عدد المواد الدراسية: {len(subjects)}")
            
            # الحصول على الميادين
            domains = LevelDataEntry.query.filter_by(database_id=database.id, entry_type='domain').all()
            print(f"عدد الميادين: {len(domains)}")
            
            # عرض عدد الميادين لكل مادة
            for subject in subjects:
                subject_domains = LevelDataEntry.query.filter_by(database_id=database.id, entry_type='domain', parent_id=subject.id).all()
                print(f"- {subject.name}: {len(subject_domains)} ميدان")
                
                # عرض أسماء الميادين لمادة اللغة العربية
                if subject.name == "اللغة العربية":
                    print("  ميادين اللغة العربية:")
                    for domain in subject_domains:
                        print(f"  * {domain.name}")
                
                # عرض أسماء الميادين لمادة حفظ القرآن
                if subject.name == "حفظ القرآن":
                    print("  ميادين حفظ القرآن:")
                    for domain in subject_domains:
                        print(f"  * {domain.name}")

if __name__ == "__main__":
    main()
