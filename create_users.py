#!/usr/bin/env python3
"""
سكريبت لإنشاء المستخدمين الافتراضيين
"""

from app import create_app, db
from app.models import User

def create_default_users():
    """إنشاء المستخدمين الافتراضيين"""
    app = create_app()
    
    with app.app_context():
        # إنشاء قاعدة البيانات
        db.create_all()
        print("تم إنشاء قاعدة البيانات")
        
        # التحقق من وجود المستخدمين
        existing_users = User.query.count()
        if existing_users > 0:
            print(f"يوجد {existing_users} مستخدم في قاعدة البيانات")
            users = User.query.all()
            for user in users:
                print(f"- {user.username}: {user.role} (نشط: {user.is_active})")
            return
        
        # إنشاء المستخدمين الافتراضيين
        users_data = [
            {
                'username': 'admin',
                'email': '<EMAIL>',
                'password': 'Admin@1234',
                'role': 'admin'
            },
            {
                'username': 'inspector',
                'email': '<EMAIL>',
                'password': 'Inspector#2024',
                'role': 'inspector'
            },
            {
                'username': 'teacher',
                'email': '<EMAIL>',
                'password': 'Teacher@2024',
                'role': 'teacher'
            }
        ]
        
        for user_data in users_data:
            user = User(
                username=user_data['username'],
                email=user_data['email'],
                role=user_data['role']
            )
            user.set_password(user_data['password'])
            db.session.add(user)
            print(f"تم إنشاء المستخدم: {user_data['username']}")
        
        db.session.commit()
        print("تم حفظ جميع المستخدمين بنجاح!")
        
        # التحقق من المستخدمين المنشأين
        print("\nالمستخدمون المتاحون:")
        users = User.query.all()
        for user in users:
            print(f"- اسم المستخدم: {user.username}")
            print(f"  كلمة المرور: {users_data[users.index(next(u for u in users_data if u['username'] == user.username))]['password']}")
            print(f"  الدور: {user.role}")
            print(f"  نشط: {user.is_active}")
            print()

if __name__ == '__main__':
    create_default_users()
