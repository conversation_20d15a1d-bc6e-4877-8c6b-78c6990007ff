#!/usr/bin/env python3
"""
سكريبت لإضافة عمود priority إلى جدول inspector_teacher_notification
"""

from app import create_app, db
from sqlalchemy import text

def add_priority_column():
    """إضافة عمود priority إلى جدول inspector_teacher_notification"""
    app = create_app()
    
    with app.app_context():
        try:
            # التحقق من وجود العمود
            result = db.session.execute(text("PRAGMA table_info(inspector_teacher_notification)"))
            columns = [row[1] for row in result.fetchall()]
            
            if 'priority' not in columns:
                print("🔧 إضافة عمود priority إلى جدول inspector_teacher_notification...")
                
                # إضافة العمود الجديد
                db.session.execute(text(
                    "ALTER TABLE inspector_teacher_notification ADD COLUMN priority VARCHAR(20) DEFAULT 'normal' NOT NULL"
                ))
                
                db.session.commit()
                print("✅ تم إضافة عمود priority بنجاح!")
                
                # التحقق من النتيجة
                result = db.session.execute(text("PRAGMA table_info(inspector_teacher_notification)"))
                columns = [row[1] for row in result.fetchall()]
                print(f"📋 أعمدة الجدول الحالية: {columns}")
                
            else:
                print("ℹ️ عمود priority موجود بالفعل في الجدول")
                
        except Exception as e:
            print(f"❌ خطأ في إضافة العمود: {str(e)}")
            db.session.rollback()
            return False
            
        return True

def verify_priority_column():
    """التحقق من وجود عمود priority وقيمه"""
    app = create_app()
    
    with app.app_context():
        try:
            # التحقق من بنية الجدول
            result = db.session.execute(text("PRAGMA table_info(inspector_teacher_notification)"))
            table_info = result.fetchall()
            
            print("📋 بنية جدول inspector_teacher_notification:")
            for row in table_info:
                print(f"   {row[1]} ({row[2]}) - Default: {row[4]} - NotNull: {row[3]}")
            
            # التحقق من البيانات الموجودة
            result = db.session.execute(text("SELECT COUNT(*) FROM inspector_teacher_notification"))
            count = result.fetchone()[0]
            print(f"📊 عدد الإشعارات في الجدول: {count}")
            
            if count > 0:
                result = db.session.execute(text("SELECT id, title, priority FROM inspector_teacher_notification LIMIT 5"))
                notifications = result.fetchall()
                print("📝 عينة من الإشعارات:")
                for notification in notifications:
                    print(f"   ID: {notification[0]}, Title: {notification[1][:30]}..., Priority: {notification[2]}")
                    
        except Exception as e:
            print(f"❌ خطأ في التحقق: {str(e)}")

def update_existing_notifications():
    """تحديث الإشعارات الموجودة لتحتوي على أولوية افتراضية"""
    app = create_app()
    
    with app.app_context():
        try:
            # تحديث الإشعارات التي لا تحتوي على أولوية
            result = db.session.execute(text(
                "UPDATE inspector_teacher_notification SET priority = 'normal' WHERE priority IS NULL OR priority = ''"
            ))
            
            updated_count = result.rowcount
            db.session.commit()
            
            print(f"✅ تم تحديث {updated_count} إشعار بالأولوية الافتراضية 'normal'")
            
        except Exception as e:
            print(f"❌ خطأ في تحديث الإشعارات: {str(e)}")
            db.session.rollback()

if __name__ == "__main__":
    print("🔧 تحديث جدول inspector_teacher_notification")
    print("=" * 50)
    
    # إضافة العمود
    print("\n1️⃣ إضافة عمود priority:")
    success = add_priority_column()
    
    if success:
        # تحديث الإشعارات الموجودة
        print("\n2️⃣ تحديث الإشعارات الموجودة:")
        update_existing_notifications()
        
        # التحقق من النتيجة
        print("\n3️⃣ التحقق من النتيجة:")
        verify_priority_column()
        
        print("\n✅ تم الانتهاء من تحديث قاعدة البيانات!")
    else:
        print("\n❌ فشل في تحديث قاعدة البيانات!")
