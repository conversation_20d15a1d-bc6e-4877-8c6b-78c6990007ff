# تعليمات استخدام التطبيق الجديد

## المشكلة

بعد عدة محاولات لحل مشكلة `sqlalchemy.exc.OperationalError: no such column: educational_level.is_active`، قمنا بإنشاء تطبيق جديد من الصفر لتجنب أي مشاكل في هيكل قاعدة البيانات.

## كيفية استخدام التطبيق الجديد

### 1. تشغيل التطبيق الجديد

قم بتشغيل التطبيق الجديد باستخدام الأمر التالي:
```
python app_new.py
```

هذا سيقوم بما يلي:
- إنشاء قاعدة بيانات جديدة باسم `ta9affi_new.db`
- إنشاء جميع الجداول بالهيكل الصحيح
- إضافة البيانات التجريبية (المستخدمين، المستويات، قواعد البيانات المنفصلة)

### 2. الوصول إلى التطبيق

افتح المتصفح وانتقل إلى:
```
http://127.0.0.1:5000
```

### 3. تسجيل الدخول

قم بتسجيل الدخول باستخدام حساب الإدارة:
- اسم المستخدم: `admin`
- كلمة المرور: `admin123`

### 4. الوصول إلى إدارة قواعد البيانات المنفصلة

بعد تسجيل الدخول، انتقل إلى:
```
http://127.0.0.1:5000/admin/databases
```

## ملاحظات هامة

1. هذا تطبيق جديد تمامًا يستخدم قاعدة بيانات مختلفة عن التطبيق القديم.
2. تم تصميم النماذج بعناية لتجنب أي مشاكل مع أسماء الأعمدة المحجوزة.
3. تم استخدام نفس هيكل المجلدات والقوالب من التطبيق القديم.

## حسابات المستخدمين التجريبية

### حساب الإدارة
- اسم المستخدم: `admin`
- كلمة المرور: `admin123`

### حساب المفتش
- اسم المستخدم: `inspector`
- كلمة المرور: `inspector123`

### حساب الأستاذ
- اسم المستخدم: `teacher`
- كلمة المرور: `teacher123`

### حساب أستاذ آخر
- اسم المستخدم: `teacher2`
- كلمة المرور: `teacher123`

## الانتقال من التطبيق القديم إلى الجديد

إذا كنت ترغب في نقل البيانات من التطبيق القديم إلى الجديد، يمكنك استخدام أدوات استيراد/تصدير البيانات المتاحة في التطبيق.

## الاستمرار في تطوير التطبيق

يمكنك الاستمرار في تطوير التطبيق الجديد بإضافة المزيد من الميزات والوظائف حسب الحاجة.
