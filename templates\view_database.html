{% extends 'base.html' %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <h2 class="mb-4">
            قاعدة بيانات: {{ database.name }} ({{ database.level.name }})
            <div class="float-end">
                <button class="btn btn-primary btn-sm me-2" data-bs-toggle="modal" data-bs-target="#importDatabaseModal">
                    <i class="fas fa-file-import me-1"></i> استيراد من Excel
                </button>
                <a href="{{ url_for('admin.export_database_data', db_id=database.id) }}" class="btn btn-success btn-sm me-2">
                    <i class="fas fa-file-export me-1"></i> تصدير إلى Excel
                </a>
                <a href="{{ url_for('admin.manage_databases') }}" class="btn btn-secondary btn-sm">
                    <i class="fas fa-arrow-right me-1"></i> العودة
                </a>
            </div>
        </h2>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="card mb-4">
            <div class="card-header">
                <ul class="nav nav-tabs card-header-tabs" id="databaseTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="subjects-tab" data-bs-toggle="tab" data-bs-target="#subjects" type="button" role="tab" aria-controls="subjects" aria-selected="true">المواد الدراسية</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="domains-tab" data-bs-toggle="tab" data-bs-target="#domains" type="button" role="tab" aria-controls="domains" aria-selected="false">الميادين</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="materials-tab" data-bs-toggle="tab" data-bs-target="#materials" type="button" role="tab" aria-controls="materials" aria-selected="false">المواد المعرفية</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="competencies-tab" data-bs-toggle="tab" data-bs-target="#competencies" type="button" role="tab" aria-controls="competencies" aria-selected="false">الكفاءات المستهدفة</button>
                    </li>
                </ul>
            </div>
            <div class="card-body">
                <div class="tab-content" id="databaseTabsContent">
                    <!-- المواد الدراسية -->
                    <div class="tab-pane fade show active" id="subjects" role="tabpanel" aria-labelledby="subjects-tab">
                        <div class="d-flex justify-content-between mb-3">
                            <h5>المواد الدراسية</h5>
                            <div>
                                <a href="{{ url_for('export_database_entry_type', db_id=database.id, entry_type='subject') }}" class="btn btn-info btn-sm me-2">
                                    <i class="fas fa-file-export me-1"></i> تصدير
                                </a>
                                <button class="btn btn-primary btn-sm me-2" data-bs-toggle="modal" data-bs-target="#addEntryModal" data-type="subject" data-parent-id="0">
                                    <i class="fas fa-plus me-1"></i> إضافة مادة
                                </button>
                                <button class="btn btn-success btn-sm" data-bs-toggle="modal" data-bs-target="#addMultipleEntriesModal" data-type="subject" data-parent-id="0">
                                    <i class="fas fa-plus-circle me-1"></i> إضافة متعددة
                                </button>
                            </div>
                        </div>
                        <div class="table-responsive">
                            <table class="table table-bordered table-hover">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>اسم المادة</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for entry in entries %}
                                    {% if entry.entry_type == 'subject' %}
                                    <tr>
                                        <td>{{ loop.index }}</td>
                                        <td>{{ entry.name }}</td>
                                        <td>
                                            {% if entry.is_active %}
                                            <span class="badge bg-success">مفعلة</span>
                                            {% else %}
                                            <span class="badge bg-danger">معطلة</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <button class="btn btn-sm btn-warning edit-entry" data-id="{{ entry.id }}" data-type="{{ entry.entry_type }}" data-name="{{ entry.name }}" data-description="{{ entry.description or '' }}" data-is-active="{{ entry.is_active }}">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-sm btn-danger delete-entry" data-id="{{ entry.id }}">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                            <button class="btn btn-sm btn-primary add-child" data-id="{{ entry.id }}" data-type="domain" data-name="{{ entry.name }}">
                                                <i class="fas fa-plus"></i> ميدان
                                            </button>
                                        </td>
                                    </tr>
                                    {% endif %}
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- الميادين -->
                    <div class="tab-pane fade" id="domains" role="tabpanel" aria-labelledby="domains-tab">
                        <div class="d-flex justify-content-between mb-3">
                            <h5>الميادين</h5>
                            <div>
                                <a href="{{ url_for('export_database_entry_type', db_id=database.id, entry_type='domain') }}" class="btn btn-info btn-sm me-2">
                                    <i class="fas fa-file-export me-1"></i> تصدير
                                </a>
                                <button class="btn btn-success btn-sm" data-bs-toggle="modal" data-bs-target="#addMultipleEntriesModal" data-type="domain" data-parent-id="0">
                                    <i class="fas fa-plus-circle me-1"></i> إضافة متعددة
                                </button>
                            </div>
                        </div>
                        <div class="table-responsive">
                            <table class="table table-bordered table-hover">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>المادة</th>
                                        <th>اسم الميدان</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for entry in entries %}
                                    {% if entry.entry_type == 'domain' %}
                                    <tr>
                                        <td>{{ loop.index }}</td>
                                        <td>
                                            {% for parent in entries %}
                                            {% if parent.id == entry.parent_id %}
                                            {{ parent.name }}
                                            {% endif %}
                                            {% endfor %}
                                        </td>
                                        <td>{{ entry.name }}</td>
                                        <td>
                                            {% if entry.is_active %}
                                            <span class="badge bg-success">مفعل</span>
                                            {% else %}
                                            <span class="badge bg-danger">معطل</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <button class="btn btn-sm btn-warning edit-entry" data-id="{{ entry.id }}" data-type="{{ entry.entry_type }}" data-name="{{ entry.name }}" data-description="{{ entry.description or '' }}" data-is-active="{{ entry.is_active }}">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-sm btn-danger delete-entry" data-id="{{ entry.id }}">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                            <button class="btn btn-sm btn-primary add-child" data-id="{{ entry.id }}" data-type="material" data-name="{{ entry.name }}">
                                                <i class="fas fa-plus"></i> مادة معرفية
                                            </button>
                                        </td>
                                    </tr>
                                    {% endif %}
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- المواد المعرفية -->
                    <div class="tab-pane fade" id="materials" role="tabpanel" aria-labelledby="materials-tab">
                        <div class="d-flex justify-content-between mb-3">
                            <h5>المواد المعرفية</h5>
                            <div>
                                <a href="{{ url_for('export_database_entry_type', db_id=database.id, entry_type='material') }}" class="btn btn-info btn-sm me-2">
                                    <i class="fas fa-file-export me-1"></i> تصدير
                                </a>
                                <button class="btn btn-success btn-sm" data-bs-toggle="modal" data-bs-target="#addMultipleEntriesModal" data-type="material" data-parent-id="0">
                                    <i class="fas fa-plus-circle me-1"></i> إضافة متعددة
                                </button>
                            </div>
                        </div>
                        <div class="table-responsive">
                            <table class="table table-bordered table-hover">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>الميدان</th>
                                        <th>اسم المادة المعرفية</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for entry in entries %}
                                    {% if entry.entry_type == 'material' %}
                                    <tr>
                                        <td>{{ loop.index }}</td>
                                        <td>
                                            {% for parent in entries %}
                                            {% if parent.id == entry.parent_id %}
                                            {{ parent.name }}
                                            {% endif %}
                                            {% endfor %}
                                        </td>
                                        <td>{{ entry.name }}</td>
                                        <td>
                                            {% if entry.is_active %}
                                            <span class="badge bg-success">مفعلة</span>
                                            {% else %}
                                            <span class="badge bg-danger">معطلة</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <button class="btn btn-sm btn-warning edit-entry" data-id="{{ entry.id }}" data-type="{{ entry.entry_type }}" data-name="{{ entry.name }}" data-description="{{ entry.description or '' }}" data-is-active="{{ entry.is_active }}">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-sm btn-danger delete-entry" data-id="{{ entry.id }}">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                            <button class="btn btn-sm btn-primary add-child" data-id="{{ entry.id }}" data-type="competency" data-name="{{ entry.name }}">
                                                <i class="fas fa-plus"></i> كفاءة
                                            </button>
                                        </td>
                                    </tr>
                                    {% endif %}
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- الكفاءات المستهدفة -->
                    <div class="tab-pane fade" id="competencies" role="tabpanel" aria-labelledby="competencies-tab">
                        <div class="d-flex justify-content-between mb-3">
                            <h5>الكفاءات المستهدفة</h5>
                            <div>
                                <a href="{{ url_for('export_database_entry_type', db_id=database.id, entry_type='competency') }}" class="btn btn-info btn-sm me-2">
                                    <i class="fas fa-file-export me-1"></i> تصدير
                                </a>
                                <button class="btn btn-success btn-sm" data-bs-toggle="modal" data-bs-target="#addMultipleEntriesModal" data-type="competency" data-parent-id="0">
                                    <i class="fas fa-plus-circle me-1"></i> إضافة متعددة
                                </button>
                            </div>
                        </div>
                        <div class="table-responsive">
                            <table class="table table-bordered table-hover">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>المادة المعرفية</th>
                                        <th>وصف الكفاءة</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for entry in entries %}
                                    {% if entry.entry_type == 'competency' %}
                                    <tr>
                                        <td>{{ loop.index }}</td>
                                        <td>
                                            {% for parent in entries %}
                                            {% if parent.id == entry.parent_id %}
                                            {{ parent.name }}
                                            {% endif %}
                                            {% endfor %}
                                        </td>
                                        <td>{{ entry.description }}</td>
                                        <td>
                                            {% if entry.is_active %}
                                            <span class="badge bg-success">مفعلة</span>
                                            {% else %}
                                            <span class="badge bg-danger">معطلة</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <button class="btn btn-sm btn-warning edit-entry" data-id="{{ entry.id }}" data-type="{{ entry.entry_type }}" data-name="{{ entry.name }}" data-description="{{ entry.description or '' }}" data-is-active="{{ entry.is_active }}">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-sm btn-danger delete-entry" data-id="{{ entry.id }}">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </td>
                                    </tr>
                                    {% endif %}
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Entry Modal -->
<div class="modal fade" id="addEntryModal" tabindex="-1" aria-labelledby="addEntryModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addEntryModalLabel">إضافة عنصر جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="addEntryForm" method="POST" action="{{ url_for('add_database_entry', db_id=database.id) }}">
                <div class="modal-body">
                    <input type="hidden" id="entry_type" name="entry_type" value="">
                    <input type="hidden" id="parent_id" name="parent_id" value="0">

                    <div class="mb-3">
                        <label for="entry_name" class="form-label">الاسم</label>
                        <input type="text" class="form-control" id="entry_name" name="name" required>
                    </div>

                    <div class="mb-3">
                        <label for="entry_description" class="form-label">الوصف</label>
                        <textarea class="form-control" id="entry_description" name="description" rows="3"></textarea>
                    </div>

                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="entry_is_active" name="is_active" checked>
                        <label class="form-check-label" for="entry_is_active">
                            تفعيل العنصر
                        </label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Entry Modal -->
<div class="modal fade" id="editEntryModal" tabindex="-1" aria-labelledby="editEntryModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editEntryModalLabel">تعديل عنصر</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="editEntryForm" method="POST">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="edit_entry_name" class="form-label">الاسم</label>
                        <input type="text" class="form-control" id="edit_entry_name" name="name" required>
                    </div>

                    <div class="mb-3">
                        <label for="edit_entry_description" class="form-label">الوصف</label>
                        <textarea class="form-control" id="edit_entry_description" name="description" rows="3"></textarea>
                    </div>

                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="edit_entry_is_active" name="is_active">
                        <label class="form-check-label" for="edit_entry_is_active">
                            تفعيل العنصر
                        </label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Entry Confirmation Modal -->
<div class="modal fade" id="deleteEntryModal" tabindex="-1" aria-labelledby="deleteEntryModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteEntryModalLabel">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                هل أنت متأكد من حذف هذا العنصر؟ سيتم حذف جميع العناصر المرتبطة به أيضاً.
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form id="deleteEntryForm" method="POST">
                    <button type="submit" class="btn btn-danger">حذف</button>
                </form>
            </div>
        </div>
    </div>
</div>

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Add Entry Modal
        const addEntryModal = document.getElementById('addEntryModal');
        const entryTypeInput = document.getElementById('entry_type');
        const parentIdInput = document.getElementById('parent_id');
        const addEntryModalTitle = document.getElementById('addEntryModalLabel');

        addEntryModal.addEventListener('show.bs.modal', function(event) {
            const button = event.relatedTarget;
            const type = button.getAttribute('data-type');
            const parentId = button.getAttribute('data-parent-id');
            const parentName = button.getAttribute('data-name');

            entryTypeInput.value = type;
            parentIdInput.value = parentId;

            let title = '';
            switch(type) {
                case 'subject':
                    title = 'إضافة مادة دراسية جديدة';
                    break;
                case 'domain':
                    title = `إضافة ميدان جديد للمادة: ${parentName}`;
                    break;
                case 'material':
                    title = `إضافة مادة معرفية جديدة للميدان: ${parentName}`;
                    break;
                case 'competency':
                    title = `إضافة كفاءة مستهدفة جديدة للمادة المعرفية: ${parentName}`;
                    break;
            }

            addEntryModalTitle.textContent = title;
        });

        // Edit Entry Modal
        const editEntryModal = document.getElementById('editEntryModal');
        const editEntryForm = document.getElementById('editEntryForm');
        const editEntryName = document.getElementById('edit_entry_name');
        const editEntryDescription = document.getElementById('edit_entry_description');
        const editEntryIsActive = document.getElementById('edit_entry_is_active');

        document.querySelectorAll('.edit-entry').forEach(button => {
            button.addEventListener('click', function() {
                const id = this.getAttribute('data-id');
                const name = this.getAttribute('data-name');
                const description = this.getAttribute('data-description');
                const isActive = this.getAttribute('data-is-active') === 'True';

                editEntryForm.action = "{{ url_for('edit_database_entry', db_id=database.id, entry_id=0) }}".replace('0', id);
                editEntryName.value = name;
                editEntryDescription.value = description;
                editEntryIsActive.checked = isActive;

                const modal = new bootstrap.Modal(editEntryModal);
                modal.show();
            });
        });

        // Add Child Entry
        document.querySelectorAll('.add-child').forEach(button => {
            button.addEventListener('click', function() {
                const id = this.getAttribute('data-id');
                const type = this.getAttribute('data-type');
                const name = this.getAttribute('data-name');

                entryTypeInput.value = type;
                parentIdInput.value = id;

                let title = '';
                switch(type) {
                    case 'domain':
                        title = `إضافة ميدان جديد للمادة: ${name}`;
                        break;
                    case 'material':
                        title = `إضافة مادة معرفية جديدة للميدان: ${name}`;
                        break;
                    case 'competency':
                        title = `إضافة كفاءة مستهدفة جديدة للمادة المعرفية: ${name}`;
                        break;
                }

                addEntryModalTitle.textContent = title;

                const modal = new bootstrap.Modal(addEntryModal);
                modal.show();
            });
        });

        // Delete Entry
        const deleteEntryModal = document.getElementById('deleteEntryModal');
        const deleteEntryForm = document.getElementById('deleteEntryForm');

        document.querySelectorAll('.delete-entry').forEach(button => {
            button.addEventListener('click', function() {
                const id = this.getAttribute('data-id');

                deleteEntryForm.action = "{{ url_for('admin.delete_database_entry', db_id=database.id, entry_id=0) }}".replace('0', id);

                const modal = new bootstrap.Modal(deleteEntryModal);
                modal.show();
            });
        });
    });
</script>
{% endblock %}

<!-- Import Database Modal -->
<div class="modal fade" id="importDatabaseModal" tabindex="-1" aria-labelledby="importDatabaseModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="importDatabaseModalLabel">استيراد بيانات من Excel</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="importDatabaseForm" method="POST" action="{{ url_for('admin.import_database_data', db_id=database.id) }}" enctype="multipart/form-data">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="import_type" class="form-label">نوع البيانات</label>
                        <select class="form-select" id="import_type" name="import_type" required>
                            <option value="all" selected>جميع البيانات</option>
                            <option value="subject">المواد الدراسية</option>
                            <option value="domain">الميادين</option>
                            <option value="material">المواد المعرفية</option>
                            <option value="competency">الكفاءات المستهدفة</option>
                            <option value="hierarchical">البيانات المترابطة</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="import_file" class="form-label">ملف Excel</label>
                        <input type="file" class="form-control" id="import_file" name="file" accept=".xlsx" required>
                        <div class="form-text">يجب أن يكون الملف بصيغة Excel (.xlsx) ويحتوي على الأعمدة المطلوبة.</div>
                    </div>
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="clear_existing" name="clear_existing">
                        <label class="form-check-label" for="clear_existing">
                            حذف البيانات الحالية قبل الاستيراد
                        </label>
                    </div>
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-1"></i> تنبيه: الرجاء التأكد من أن الملف يحتوي على البيانات بالتنسيق الصحيح. استخدم ملفات تم تصديرها من النظام كنموذج.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">استيراد</button>
                </div>
            </form>
        </div>
    </div>
</div>
<!-- Add Multiple Entries Modal -->
<div class="modal fade" id="addMultipleEntriesModal" tabindex="-1" aria-labelledby="addMultipleEntriesModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addMultipleEntriesModalLabel">إضافة عناصر متعددة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="addMultipleEntriesForm" method="POST" action="{{ url_for('admin.add_database_entry', db_id=database.id) }}">
                <div class="modal-body">
                    <input type="hidden" id="multiple_entry_type" name="entry_type" value="">
                    <input type="hidden" id="multiple_parent_id" name="parent_id" value="0">
                    <input type="hidden" name="is_multiple" value="true">

                    <div class="mb-3">
                        <label for="parent_selector" class="form-label">العنصر الأب</label>
                        <select class="form-select" id="parent_selector" name="parent_selector">
                            <option value="0">اختر العنصر الأب</option>
                            <!-- سيتم ملؤها بواسطة JavaScript -->
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="multiple_entries" class="form-label">العناصر (كل عنصر في سطر جديد)</label>
                        <textarea class="form-control" id="multiple_entries" name="multiple_entries" rows="10" placeholder="أدخل كل عنصر في سطر جديد" required></textarea>
                        <div class="form-text">أدخل كل عنصر في سطر جديد. يمكنك لصق قائمة من Excel أو أي مصدر آخر.</div>
                    </div>

                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="multiple_is_active" name="is_active" checked>
                        <label class="form-check-label" for="multiple_is_active">
                            مفعل
                        </label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">إضافة</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
    // تحديث مودال الإضافة المتعددة
    document.addEventListener('DOMContentLoaded', function() {
        const addMultipleEntriesModal = document.getElementById('addMultipleEntriesModal');
        if (addMultipleEntriesModal) {
            addMultipleEntriesModal.addEventListener('show.bs.modal', function(event) {
                const button = event.relatedTarget;
                const type = button.getAttribute('data-type');
                const parentId = button.getAttribute('data-parent-id');
                const parentName = button.getAttribute('data-name');

                // تحديث العنوان
                const modalTitle = addMultipleEntriesModal.querySelector('.modal-title');
                let title = 'إضافة عناصر متعددة';

                if (type === 'subject') {
                    title = 'إضافة مواد دراسية متعددة';
                } else if (type === 'domain') {
                    title = 'إضافة ميادين متعددة';
                    if (parentName) {
                        title += ' للمادة: ' + parentName;
                    }
                } else if (type === 'material') {
                    title = 'إضافة مواد معرفية متعددة';
                    if (parentName) {
                        title += ' للميدان: ' + parentName;
                    }
                } else if (type === 'competency') {
                    title = 'إضافة كفاءات متعددة';
                    if (parentName) {
                        title += ' للمادة المعرفية: ' + parentName;
                    }
                }

                modalTitle.textContent = title;

                // تحديث الحقول المخفية
                document.getElementById('multiple_entry_type').value = type;
                document.getElementById('multiple_parent_id').value = parentId;

                // تحديث قائمة العناصر الأب
                const parentSelector = document.getElementById('parent_selector');
                parentSelector.innerHTML = '<option value="0">اختر العنصر الأب</option>';

                // إظهار/إخفاء قائمة العناصر الأب حسب النوع
                const parentSelectorContainer = parentSelector.closest('.mb-3');

                if (type === 'subject') {
                    // لا تحتاج المواد الدراسية إلى عنصر أب
                    parentSelectorContainer.style.display = 'none';
                } else {
                    parentSelectorContainer.style.display = 'block';

                    // تحميل العناصر الأب المناسبة
                    let parentType = '';
                    if (type === 'domain') parentType = 'subject';
                    else if (type === 'material') parentType = 'domain';
                    else if (type === 'competency') parentType = 'material';

                    // تحميل العناصر الأب من الصفحة
                    const entries = document.querySelectorAll('tr');
                    entries.forEach(entry => {
                        const entryTypeCell = entry.querySelector(`[data-type="${parentType}"]`);
                        if (entryTypeCell) {
                            const entryId = entryTypeCell.getAttribute('data-id');
                            const entryName = entryTypeCell.getAttribute('data-name');
                            if (entryId && entryName) {
                                const option = document.createElement('option');
                                option.value = entryId;
                                option.textContent = entryName;
                                parentSelector.appendChild(option);
                            }
                        }
                    });

                    // إذا تم تحديد عنصر أب
                    if (parentId && parentId !== '0') {
                        parentSelector.value = parentId;
                    }
                }
            });

            // تحديث العنصر الأب عند تغيير الاختيار
            const parentSelector = document.getElementById('parent_selector');
            parentSelector.addEventListener('change', function() {
                document.getElementById('multiple_parent_id').value = this.value;
            });

            // تقديم نموذج الإضافة المتعددة
            const addMultipleEntriesForm = document.getElementById('addMultipleEntriesForm');
            addMultipleEntriesForm.addEventListener('submit', function(event) {
                event.preventDefault();

                const formData = new FormData(this);
                const entriesText = formData.get('multiple_entries');
                const entries = entriesText.split('\n').filter(entry => entry.trim() !== '');

                if (entries.length === 0) {
                    alert('الرجاء إدخال عنصر واحد على الأقل');
                    return;
                }

                // إرسال البيانات إلى الخادم
                fetch(this.action, {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // إعادة تحميل الصفحة
                        window.location.reload();
                    } else {
                        alert('حدث خطأ: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('حدث خطأ أثناء معالجة الطلب');
                });
            });
        }
    });
</script>

{% endblock %}