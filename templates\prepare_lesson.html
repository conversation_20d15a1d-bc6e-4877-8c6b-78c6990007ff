{% extends 'base.html' %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card mb-4">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <i class="fas fa-chalkboard-teacher me-1"></i>
                            تحضير خطة درس باستخدام الذكاء الاصطناعي
                        </div>
                        <div>
                            <a href="{{ url_for('teacher_dashboard') }}" class="btn btn-sm btn-outline-secondary">
                                <i class="fas fa-arrow-left me-1"></i> العودة إلى لوحة التحكم
                            </a>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <div class="alert alert-info">
                                <h5 class="alert-heading">معلومات الدرس</h5>
                                <hr>
                                <div class="row">
                                    <div class="col-md-6">
                                        <p><strong>المستوى:</strong> {{ lesson_info.level }}</p>
                                        <p><strong>المادة:</strong> {{ lesson_info.subject }}</p>
                                        <p><strong>الميدان:</strong> {{ lesson_info.domain }}</p>
                                    </div>
                                    <div class="col-md-6">
                                        <p><strong>المادة المعرفية:</strong> {{ lesson_info.material }}</p>
                                        <p><strong>الكفاءة المستهدفة:</strong> {{ lesson_info.competency }}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mb-4">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header">
                                    <i class="fas fa-cog me-1"></i> إعدادات تحضير الدرس
                                </div>
                                <div class="card-body">
                                    <form id="lessonPlanForm">
                                        <div class="row">
                                            <div class="col-md-6 mb-3">
                                                <label for="lessonDuration" class="form-label">مدة الدرس (بالدقائق)</label>
                                                <input type="number" class="form-control" id="lessonDuration" name="lessonDuration" value="45" min="15" max="180">
                                            </div>
                                            <div class="col-md-6 mb-3">
                                                <label for="studentLevel" class="form-label">مستوى الطلاب</label>
                                                <select class="form-select" id="studentLevel" name="studentLevel">
                                                    <option value="beginner">مبتدئ</option>
                                                    <option value="intermediate" selected>متوسط</option>
                                                    <option value="advanced">متقدم</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-6 mb-3">
                                                <label for="teachingMethod" class="form-label">طريقة التدريس</label>
                                                <select class="form-select" id="teachingMethod" name="teachingMethod">
                                                    <option value="interactive" selected>تفاعلي</option>
                                                    <option value="lecture">محاضرة</option>
                                                    <option value="group">عمل جماعي</option>
                                                    <option value="project">مشروع</option>
                                                    <option value="flipped">الفصل المقلوب</option>
                                                </select>
                                            </div>
                                            <div class="col-md-6 mb-3">
                                                <label for="resources" class="form-label">الموارد المتاحة</label>
                                                <select class="form-select" id="resources" name="resources" multiple>
                                                    <option value="textbook" selected>كتاب مدرسي</option>
                                                    <option value="projector">جهاز عرض</option>
                                                    <option value="computer">حاسوب</option>
                                                    <option value="internet">إنترنت</option>
                                                    <option value="worksheets">أوراق عمل</option>
                                                </select>
                                                <small class="form-text text-muted">اضغط Ctrl للاختيار المتعدد</small>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-12 mb-3">
                                                <label for="additionalNotes" class="form-label">ملاحظات إضافية (اختياري)</label>
                                                <textarea class="form-control" id="additionalNotes" name="additionalNotes" rows="3" placeholder="أي معلومات إضافية ترغب في إضافتها لخطة الدرس..."></textarea>
                                            </div>
                                        </div>
                                        <div class="d-grid gap-2">
                                            <button type="button" id="generatePlanBtn" class="btn btn-primary">
                                                <i class="fas fa-magic me-1"></i> توليد خطة الدرس
                                            </button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <i class="fas fa-file-alt me-1"></i> خطة الدرس
                                        </div>
                                        <div>
                                            <button id="copyPlanBtn" class="btn btn-sm btn-outline-primary me-2" disabled>
                                                <i class="fas fa-copy me-1"></i> نسخ
                                            </button>
                                            <button id="saveAsWordBtn" class="btn btn-sm btn-outline-info me-2" disabled>
                                                <i class="fas fa-file-word me-1"></i> حفظ كملف Word
                                            </button>
                                            <button id="downloadPlanBtn" class="btn btn-sm btn-outline-success me-2" disabled>
                                                <i class="fas fa-download me-1"></i> تنزيل
                                            </button>
                                            <button id="printPlanBtn" class="btn btn-sm btn-outline-secondary" disabled>
                                                <i class="fas fa-print me-1"></i> طباعة
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <div id="loadingIndicator" style="display: none;">
                                        <div class="d-flex justify-content-center">
                                            <div class="spinner-border text-primary" role="status">
                                                <span class="visually-hidden">جاري التحميل...</span>
                                            </div>
                                        </div>
                                        <p class="text-center mt-2">جاري توليد خطة الدرس باستخدام الذكاء الاصطناعي...</p>
                                    </div>
                                    <div id="lessonPlanContent" class="d-none">
                                        <div class="lesson-plan-header text-center mb-4">
                                            <h3>خطة درس: <span id="lessonTitle"></span></h3>
                                            <p class="mb-1">المستوى: {{ lesson_info.level }} | المادة: {{ lesson_info.subject }}</p>
                                            <p>الميدان: {{ lesson_info.domain }} | المادة المعرفية: {{ lesson_info.material }}</p>
                                        </div>
                                        <div id="lessonPlanBody" class="lesson-plan-body">
                                            <!-- سيتم ملء هذا القسم بواسطة JavaScript -->
                                        </div>
                                    </div>
                                    <div id="initialMessage" class="text-center py-5">
                                        <i class="fas fa-chalkboard-teacher fa-4x mb-3 text-muted"></i>
                                        <h5>قم بتعبئة النموذج واضغط على "توليد خطة الدرس" للبدء</h5>
                                        <p class="text-muted">سيتم استخدام الذكاء الاصطناعي لإنشاء خطة درس مخصصة بناءً على المعلومات المدخلة</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const generatePlanBtn = document.getElementById('generatePlanBtn');
        const copyPlanBtn = document.getElementById('copyPlanBtn');
        const saveAsWordBtn = document.getElementById('saveAsWordBtn');
        const downloadPlanBtn = document.getElementById('downloadPlanBtn');
        const printPlanBtn = document.getElementById('printPlanBtn');
        const loadingIndicator = document.getElementById('loadingIndicator');
        const initialMessage = document.getElementById('initialMessage');
        const lessonPlanContent = document.getElementById('lessonPlanContent');
        const lessonPlanBody = document.getElementById('lessonPlanBody');
        const lessonTitle = document.getElementById('lessonTitle');

        // توليد خطة الدرس
        generatePlanBtn.addEventListener('click', function() {
            // إظهار مؤشر التحميل
            loadingIndicator.style.display = 'block';
            initialMessage.style.display = 'none';
            lessonPlanContent.classList.add('d-none');

            // جمع البيانات من النموذج
            const lessonDuration = document.getElementById('lessonDuration').value;
            const studentLevel = document.getElementById('studentLevel').value;
            const teachingMethod = document.getElementById('teachingMethod').value;
            const resourcesSelect = document.getElementById('resources');
            const resources = Array.from(resourcesSelect.selectedOptions).map(option => option.value);
            const additionalNotes = document.getElementById('additionalNotes').value;

            // بيانات الدرس
            const lessonData = {
                level: "{{ lesson_info.level }}",
                subject: "{{ lesson_info.subject }}",
                domain: "{{ lesson_info.domain }}",
                material: "{{ lesson_info.material }}",
                competency: "{{ lesson_info.competency }}",
                duration: lessonDuration,
                studentLevel: studentLevel,
                teachingMethod: teachingMethod,
                resources: resources,
                additionalNotes: additionalNotes
            };

            // محاكاة طلب API (في التطبيق الحقيقي، سيتم استبدال هذا بطلب API فعلي)
            setTimeout(function() {
                // إخفاء مؤشر التحميل
                loadingIndicator.style.display = 'none';

                // إنشاء خطة درس وهمية (في التطبيق الحقيقي، ستأتي هذه البيانات من API)
                generateMockLessonPlan(lessonData);

                // إظهار خطة الدرس
                lessonPlanContent.classList.remove('d-none');

                // تفعيل أزرار النسخ والتنزيل والطباعة
                copyPlanBtn.disabled = false;
                saveAsWordBtn.disabled = false;
                downloadPlanBtn.disabled = false;
                printPlanBtn.disabled = false;
            }, 3000); // محاكاة تأخير 3 ثوانٍ
        });

        // نسخ خطة الدرس
        copyPlanBtn.addEventListener('click', function() {
            const lessonPlanText = lessonPlanContent.innerText;
            navigator.clipboard.writeText(lessonPlanText).then(function() {
                alert('تم نسخ خطة الدرس إلى الحافظة');
            }, function() {
                alert('فشل نسخ خطة الدرس');
            });
        });

        // تنزيل خطة الدرس
        downloadPlanBtn.addEventListener('click', function() {
            const lessonPlanHTML = lessonPlanContent.innerHTML;
            const blob = new Blob([`
                <html>
                <head>
                    <title>خطة درس - ${lessonTitle.innerText}</title>
                    <meta charset="UTF-8">
                    <style>
                        body { font-family: Arial, sans-serif; direction: rtl; }
                        .lesson-plan-header { text-align: center; margin-bottom: 20px; }
                        h3 { margin-bottom: 10px; }
                        .section { margin-bottom: 15px; }
                        .section-title { font-weight: bold; margin-bottom: 5px; }
                    </style>
                </head>
                <body>
                    ${lessonPlanHTML}
                </body>
                </html>
            `], {type: 'text/html'});

            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `خطة_درس_${lessonTitle.innerText.replace(/ /g, '_')}.html`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        });

        // حفظ خطة الدرس كملف Word
        saveAsWordBtn.addEventListener('click', function() {
            // إنشاء محتوى HTML منسق لملف Word
            const wordContent = `
                <html xmlns:o='urn:schemas-microsoft-com:office:office'
                      xmlns:w='urn:schemas-microsoft-com:office:word'
                      xmlns='http://www.w3.org/TR/REC-html40'>
                <head>
                    <meta charset="UTF-8">
                    <title>خطة درس - ${lessonTitle.innerText}</title>
                    <style>
                        body { font-family: 'Arial', sans-serif; direction: rtl; }
                        .lesson-plan-header { text-align: center; margin-bottom: 20px; }
                        h3 { margin-bottom: 10px; }
                        .section { margin-bottom: 15px; }
                        .section-title { font-weight: bold; margin-bottom: 5px; }
                        table { border-collapse: collapse; width: 100%; }
                        table, th, td { border: 1px solid #ddd; }
                        th, td { padding: 8px; text-align: right; }
                    </style>
                    <!--[if gte mso 9]>
                    <xml>
                        <w:WordDocument>
                            <w:View>Print</w:View>
                            <w:Zoom>90</w:Zoom>
                            <w:DoNotOptimizeForBrowser/>
                        </w:WordDocument>
                    </xml>
                    <![endif]-->
                </head>
                <body>
                    ${lessonPlanContent.innerHTML}
                </body>
                </html>
            `;

            // إنشاء Blob من المحتوى
            const blob = new Blob([wordContent], { type: 'application/msword' });

            // إنشاء URL للتنزيل
            const url = URL.createObjectURL(blob);

            // إنشاء عنصر رابط للتنزيل
            const a = document.createElement('a');
            a.href = url;
            a.download = `خطة_درس_${lessonTitle.innerText.replace(/ /g, '_')}.doc`;

            // إضافة الرابط للصفحة، النقر عليه، ثم إزالته
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);

            // تحرير الموارد
            URL.revokeObjectURL(url);
        });

        // طباعة خطة الدرس
        printPlanBtn.addEventListener('click', function() {
            const printWindow = window.open('', '_blank');
            printWindow.document.write(`
                <html>
                <head>
                    <title>خطة درس - ${lessonTitle.innerText}</title>
                    <meta charset="UTF-8">
                    <style>
                        body { font-family: Arial, sans-serif; direction: rtl; }
                        .lesson-plan-header { text-align: center; margin-bottom: 20px; }
                        h3 { margin-bottom: 10px; }
                        .section { margin-bottom: 15px; }
                        .section-title { font-weight: bold; margin-bottom: 5px; }
                        @media print {
                            body { padding: 20px; }
                        }
                    </style>
                </head>
                <body>
                    ${lessonPlanContent.innerHTML}
                </body>
                </html>
            `);
            printWindow.document.close();
            printWindow.focus();
            setTimeout(function() {
                printWindow.print();
                printWindow.close();
            }, 500);
        });

        // دالة لإنشاء خطة درس وهمية (في التطبيق الحقيقي، ستأتي هذه البيانات من API)
        function generateMockLessonPlan(data) {
            // تعيين عنوان الدرس
            lessonTitle.innerText = data.material;

            // إنشاء محتوى خطة الدرس
            let planHTML = `
                <div class="section">
                    <div class="section-title">الأهداف التعليمية:</div>
                    <ul>
                        <li>فهم ${data.material} وتطبيقاته في الحياة اليومية.</li>
                        <li>تطوير مهارات التفكير النقدي والتحليلي لدى الطلاب.</li>
                        <li>تعزيز قدرة الطلاب على حل المشكلات المتعلقة بـ ${data.material}.</li>
                    </ul>
                </div>

                <div class="section">
                    <div class="section-title">المتطلبات السابقة:</div>
                    <ul>
                        <li>معرفة أساسية بـ ${data.domain}.</li>
                        <li>فهم المفاهيم الأساسية في ${data.subject}.</li>
                    </ul>
                </div>

                <div class="section">
                    <div class="section-title">الموارد والمواد التعليمية:</div>
                    <ul>
                        ${data.resources.includes('textbook') ? '<li>الكتاب المدرسي، الصفحات 45-50.</li>' : ''}
                        ${data.resources.includes('projector') ? '<li>جهاز عرض لعرض الشرائح التوضيحية.</li>' : ''}
                        ${data.resources.includes('computer') ? '<li>أجهزة حاسوب للأنشطة التفاعلية.</li>' : ''}
                        ${data.resources.includes('internet') ? '<li>اتصال بالإنترنت للبحث وعرض الموارد عبر الإنترنت.</li>' : ''}
                        ${data.resources.includes('worksheets') ? '<li>أوراق عمل للتمارين والأنشطة.</li>' : ''}
                        <li>سبورة وأقلام ملونة.</li>
                    </ul>
                </div>

                <div class="section">
                    <div class="section-title">خطة سير الدرس (${data.duration} دقيقة):</div>

                    <div class="subsection">
                        <strong>المقدمة (10 دقائق):</strong>
                        <ul>
                            <li>تحية الطلاب ومراجعة سريعة للدرس السابق.</li>
                            <li>طرح أسئلة تحفيزية حول ${data.material} لإثارة اهتمام الطلاب.</li>
                            <li>عرض أهداف الدرس وأهميته في الحياة اليومية.</li>
                        </ul>
                    </div>

                    <div class="subsection">
                        <strong>العرض والشرح (${Math.floor(data.duration * 0.4)} دقيقة):</strong>
                        <ul>
                            <li>شرح المفاهيم الأساسية لـ ${data.material} مع استخدام أمثلة توضيحية.</li>
                            <li>عرض تطبيقات عملية لـ ${data.material} في مجالات مختلفة.</li>
                            ${data.teachingMethod === 'interactive' ? '<li>طرح أسئلة تفاعلية خلال الشرح لضمان فهم الطلاب.</li>' : ''}
                            ${data.teachingMethod === 'lecture' ? '<li>تقديم محاضرة منظمة حول الموضوع مع استخدام الوسائل البصرية.</li>' : ''}
                            ${data.teachingMethod === 'group' ? '<li>تقسيم الطلاب إلى مجموعات صغيرة لمناقشة جوانب مختلفة من الموضوع.</li>' : ''}
                            ${data.teachingMethod === 'project' ? '<li>تقديم مشروع عملي يطبق فيه الطلاب المفاهيم المتعلقة بـ ${data.material}.</li>' : ''}
                            ${data.teachingMethod === 'flipped' ? '<li>مناقشة المواد التي تم دراستها مسبقاً من قبل الطلاب في المنزل.</li>' : ''}
                        </ul>
                    </div>

                    <div class="subsection">
                        <strong>الأنشطة والتطبيق (${Math.floor(data.duration * 0.3)} دقيقة):</strong>
                        <ul>
                            ${data.teachingMethod === 'interactive' ? '<li>تنفيذ نشاط تفاعلي يطبق فيه الطلاب المفاهيم المكتسبة.</li>' : ''}
                            ${data.teachingMethod === 'group' ? '<li>عمل المجموعات على حل مشكلات متعلقة بـ ${data.material}.</li>' : ''}
                            ${data.teachingMethod === 'project' ? '<li>بدء العمل على المشروع وتقديم التوجيه للطلاب.</li>' : ''}
                            <li>حل تمارين تطبيقية من الكتاب المدرسي أو أوراق العمل.</li>
                            <li>مناقشة الحلول والإجابات مع الطلاب.</li>
                        </ul>
                    </div>

                    <div class="subsection">
                        <strong>التقييم والخاتمة (${Math.floor(data.duration * 0.2)} دقيقة):</strong>
                        <ul>
                            <li>طرح أسئلة تقييمية للتأكد من تحقيق أهداف الدرس.</li>
                            <li>تلخيص النقاط الرئيسية في الدرس.</li>
                            <li>الإعلان عن الواجب المنزلي والتحضير للدرس القادم.</li>
                            <li>الإجابة على أسئلة الطلاب واستفساراتهم.</li>
                        </ul>
                    </div>
                </div>

                <div class="section">
                    <div class="section-title">التقييم:</div>
                    <ul>
                        <li>ملاحظة مشاركة الطلاب وتفاعلهم خلال الدرس.</li>
                        <li>تقييم إجابات الطلاب على الأسئلة الشفهية.</li>
                        <li>تصحيح التمارين والأنشطة المنفذة خلال الدرس.</li>
                        <li>تقييم الواجب المنزلي في الدرس القادم.</li>
                    </ul>
                </div>

                <div class="section">
                    <div class="section-title">الواجب المنزلي:</div>
                    <ul>
                        <li>حل التمارين في الكتاب المدرسي صفحة 51-52.</li>
                        <li>إعداد ملخص قصير عن تطبيقات ${data.material} في الحياة اليومية.</li>
                    </ul>
                </div>

                <div class="section">
                    <div class="section-title">ملاحظات إضافية:</div>
                    <p>${data.additionalNotes || 'لا توجد ملاحظات إضافية.'}</p>
                </div>
            `;

            // إضافة المحتوى إلى العنصر
            lessonPlanBody.innerHTML = planHTML;
        }
    });
</script>
{% endblock %}
