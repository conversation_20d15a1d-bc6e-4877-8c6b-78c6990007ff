/* Modern UI Styles for Ta9affi application */

:root {
    /* Primary Colors */
    --primary-color: #1976d2;
    --primary-hover: #1565c0;
    --primary-light: #bbdefb;
    --primary-dark: #0d47a1;
    
    /* Secondary Colors */
    --secondary-color: #26a69a;
    --secondary-hover: #00897b;
    --secondary-light: #b2dfdb;
    
    /* Accent Colors */
    --accent-color: #ff5722;
    --accent-hover: #e64a19;
    --accent-light: #ffccbc;
    
    /* Neutral Colors */
    --light-bg: #f8f9fa;
    --light-text: #212529;
    --light-border: rgba(0, 0, 0, 0.125);
    --light-card-header: rgba(0, 0, 0, 0.03);
    --light-footer: rgba(0, 0, 0, 0.05);
    
    /* Shadows */
    --shadow-sm: 0 .125rem .25rem rgba(0, 0, 0, .075);
    --shadow-md: 0 .5rem 1rem rgba(0, 0, 0, .15);
    --shadow-lg: 0 1rem 3rem rgba(0, 0, 0, .175);
    
    /* Border Radius */
    --border-radius-sm: 0.25rem;
    --border-radius-md: 0.5rem;
    --border-radius-lg: 1rem;
    --border-radius-xl: 1.5rem;
    
    /* Transitions */
    --transition-speed: 0.3s;
}

/* Global Styles */
body {
    font-family: 'Tajawal', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: var(--light-bg);
    color: var(--light-text);
    transition: background-color var(--transition-speed), color var(--transition-speed);
}

/* Navbar Styles */
.navbar {
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-speed);
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
    letter-spacing: 0.5px;
}

.navbar-dark .navbar-nav .nav-link {
    color: rgba(255, 255, 255, 0.85);
    transition: color 0.2s;
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius-sm);
    margin: 0 0.2rem;
}

.navbar-dark .navbar-nav .nav-link:hover {
    color: #ffffff;
    background-color: rgba(255, 255, 255, 0.1);
}

.navbar-dark .navbar-nav .nav-link.active {
    color: #ffffff;
    background-color: rgba(255, 255, 255, 0.15);
}

/* Card Styles */
.card {
    border: none;
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-sm);
    transition: transform 0.3s, box-shadow 0.3s;
    overflow: hidden;
    margin-bottom: 1.5rem;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-md);
}

.card-header {
    font-weight: 600;
    border-bottom: 1px solid var(--light-border);
    background-color: #fff;
    padding: 1rem 1.25rem;
}

.card-header i {
    color: var(--primary-color);
    margin-right: 0.5rem;
}

.card-body {
    padding: 1.5rem;
}

/* Button Styles */
.btn {
    border-radius: var(--border-radius-sm);
    font-weight: 500;
    padding: 0.5rem 1.25rem;
    transition: all 0.3s;
    text-transform: none;
    letter-spacing: 0.3px;
    box-shadow: none;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-sm);
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover, .btn-primary:focus {
    background-color: var(--primary-hover);
    border-color: var(--primary-hover);
}

.btn-secondary {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
}

.btn-secondary:hover, .btn-secondary:focus {
    background-color: var(--secondary-hover);
    border-color: var(--secondary-hover);
}

.btn-accent {
    background-color: var(--accent-color);
    border-color: var(--accent-color);
    color: white;
}

.btn-accent:hover, .btn-accent:focus {
    background-color: var(--accent-hover);
    border-color: var(--accent-hover);
    color: white;
}

.btn-outline-primary {
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-outline-primary:hover {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

/* Form Styles */
.form-control, .form-select {
    border-radius: var(--border-radius-sm);
    border: 1px solid #ced4da;
    padding: 0.75rem 1rem;
    font-size: 1rem;
    transition: border-color 0.3s, box-shadow 0.3s;
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.25rem rgba(25, 118, 210, 0.25);
}

.form-floating > label {
    padding: 0.75rem 1rem;
}

.form-floating > .form-control:focus ~ label,
.form-floating > .form-control:not(:placeholder-shown) ~ label {
    color: var(--primary-color);
    transform: scale(0.85) translateY(-0.75rem) translateX(0.15rem);
}

/* Table Styles */
.table {
    border-radius: var(--border-radius-sm);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

.table thead th {
    background-color: var(--primary-light);
    color: var(--primary-dark);
    font-weight: 600;
    border-bottom: none;
    padding: 1rem;
}

.table tbody tr:nth-of-type(odd) {
    background-color: rgba(0, 0, 0, 0.02);
}

.table tbody td {
    padding: 1rem;
    vertical-align: middle;
}

/* Alert Styles */
.alert {
    border-radius: var(--border-radius-md);
    border: none;
    box-shadow: var(--shadow-sm);
    padding: 1rem 1.25rem;
}

/* Badge Styles */
.badge {
    font-weight: 500;
    padding: 0.35em 0.65em;
    border-radius: var(--border-radius-sm);
}

/* Progress Bar */
.progress {
    height: 0.75rem;
    border-radius: var(--border-radius-sm);
    background-color: var(--primary-light);
    margin: 1rem 0;
}

.progress-bar {
    background-color: var(--primary-color);
}

/* Footer Styles */
footer {
    background-color: var(--light-footer) !important;
    transition: background-color var(--transition-speed);
}

footer h5 {
    font-weight: 600;
    margin-bottom: 1.25rem;
    color: var(--primary-color);
}

footer a {
    color: var(--light-text);
    text-decoration: none;
    transition: color 0.2s;
}

footer a:hover {
    color: var(--primary-color);
    text-decoration: underline;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .card {
        margin-bottom: 1rem;
    }
    
    .card:hover {
        transform: none;
    }
    
    .btn:hover {
        transform: none;
    }
    
    .navbar-brand {
        font-size: 1.25rem;
    }
}

/* Animation Effects */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.slide-in {
    animation: slideIn 0.5s ease-out;
}

@keyframes slideIn {
    from { transform: translateY(20px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}