"""مسارات المصادقة لتطبيق Ta9affi"""

from flask import Blueprint, render_template, redirect, url_for, flash, request, session
from flask_login import login_user, logout_user, login_required, current_user
from werkzeug.security import generate_password_hash
from app.models import User, Role
from app import db, csrf, limiter
from datetime import datetime
import secrets

# إنشاء بلوبرنت للمصادقة
auth_bp = Blueprint('auth', __name__, template_folder='../../templates/auth')

def is_password_strong(password):
    """
    التحقق من قوة كلمة المرور

    يجب أن تحتوي كلمة المرور على:
    - 8 أحرف على الأقل
    - حرف كبير واحد على الأقل
    - حرف صغير واحد على الأقل
    - رقم واحد على الأقل
    - رمز خاص واحد على الأقل

    Returns:
        tuple: (قوة كلمة المرور (صحيح/خطأ، رسالة الخطأ
    """
    if len(password) < 8:
        return False, "يجب أن تكون كلمة المرور 8 أحرف على الأقل."

    if not any(c.isupper() for c in password):
        return False, "يجب أن تحتوي كلمة المرور على حرف كبير واحد على الأقل."

    if not any(c.islower() for c in password):
        return False, "يجب أن تحتوي كلمة المرور على حرف صغير واحد على الأقل."

    if not any(c.isdigit() for c in password):
        return False, "يجب أن تحتوي كلمة المرور على رقم واحد على الأقل."

    if not any(c in "!@#$%^&*()_+-=[]{}|;:'\",.<>/?" for c in password):
        return False, "يجب أن تحتوي كلمة المرور على رمز خاص واحد على الأقل."

    return True, ""

@auth_bp.route('/login', methods=['GET', 'POST'])
@limiter.limit("5 per minute")
def login():
    """
    مسار تسجيل الدخول مع حماية من هجمات القوة الغاشمة
    """
    # إذا كان المستخدم مسجل دخوله بالفعل، توجيهه إلى لوحة التحكم
    if current_user.is_authenticated:
        return redirect(url_for('main.dashboard'))

    # التحقق من محاولات تسجيل الدخول الفاشلة
    login_attempts = session.get('login_attempts', 0)
    last_attempt = session.get('last_attempt', 0)

    # معالجة نموذج تسجيل الدخول
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        remember = True if request.form.get('remember') else False

        # البحث عن المستخدم في قاعدة البيانات
        user = User.query.filter_by(username=username).first()

        # التحقق من وجود المستخدم وصحة كلمة المرور
        if user and user.is_active and user.check_password(password):
            # إعادة تعيين محاولات تسجيل الدخول
            session.pop('login_attempts', None)
            session.pop('last_attempt', None)

            # تسجيل الدخول مع تذكر المستخدم إذا تم اختيار ذلك
            login_user(user, remember=remember)

            # تجديد معرف الجلسة لمنع هجمات اختطاف الجلسة
            session.regenerate()

            flash('تم تسجيل الدخول بنجاح!', 'success')

            # التحقق من صحة الصفحة التالية لمنع إعادة التوجيه الخطرة
            next_page = request.args.get('next')
            if next_page and not next_page.startswith('/'):
                next_page = None

            return redirect(next_page or url_for('main.dashboard'))
        else:
            # زيادة عدد محاولات تسجيل الدخول الفاشلة
            session['login_attempts'] = login_attempts + 1
            session['last_attempt'] = int(datetime.utcnow().timestamp())

            if not user:
                flash('اسم المستخدم غير صحيح', 'danger')
            elif not user.check_password(password):
                flash('كلمة المرور غير صحيحة', 'danger')
            elif not user.is_active:
                flash('الحساب معطل، يرجى التواصل مع الإدارة', 'warning')

    return render_template('auth/login.html')

@auth_bp.route('/register', methods=['GET', 'POST'])
@limiter.limit("10 per hour")
def register():
    """
    مسار التسجيل (متاح فقط للمدير) مع تحسينات أمنية
    """
    # التحقق من أن المستخدم مسجل دخوله ولديه صلاحيات المدير
    if not current_user.is_authenticated or not current_user.is_admin():
        flash('غير مصرح لك بالوصول إلى هذه الصفحة.', 'danger')
        return redirect(url_for('main.dashboard'))

    # معالجة نموذج التسجيل
    if request.method == 'POST':
        username = request.form.get('username')
        email = request.form.get('email')
        password = request.form.get('password')
        confirm_password = request.form.get('confirm_password')
        role = request.form.get('role')

        # التحقق من تطابق كلمتي المرور
        if password != confirm_password:
            flash('كلمتا المرور غير متطابقتين.', 'danger')
            return render_template('auth/register.html')

        # التحقق من قوة كلمة المرور
        is_strong, error_message = is_password_strong(password)
        if not is_strong:
            flash(error_message, 'danger')
            return render_template('auth/register.html')

        # التحقق من عدم وجود مستخدم بنفس اسم المستخدم أو البريد الإلكتروني
        existing_user = User.query.filter((User.username == username) | (User.email == email)).first()
        if existing_user:
            flash('اسم المستخدم أو البريد الإلكتروني مستخدم بالفعل.', 'danger')
            return render_template('auth/register.html')

        # إنشاء مستخدم جديد
        new_user = User(username=username, email=email, role=role)
        new_user.set_password(password)

        # حفظ المستخدم في قاعدة البيانات
        try:
            db.session.add(new_user)
            db.session.commit()
            flash(f'تم إنشاء المستخدم {username} بنجاح!', 'success')
            return redirect(url_for('auth.login'))
        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ أثناء إنشاء المستخدم: {str(e)}', 'danger')

    return render_template('auth/register.html')

@auth_bp.route('/logout')
@login_required
def logout():
    """
    مسار تسجيل الخروج مع تنظيف بيانات الجلسة
    """
    # تسجيل الخروج من Flask-Login
    logout_user()

    # إعادة تعيين معرف الجلسة لمنع هجمات إعادة تشغيل الجلسة
    session.clear()
    session.regenerate()

    flash('تم تسجيل الخروج بنجاح.', 'info')
    return redirect(url_for('auth.login'))

# بيانات الاعتماد الافتراضية
DEFAULT_CREDENTIALS = {
    'admin': {'username': 'admin', 'password': 'Admin@1234', 'role': 'admin'},
    'teacher': {'username': 'teacher', 'password': 'Teacher@2024', 'role': 'teacher'},
    'inspector': {'username': 'inspector', 'password': 'Inspector#2024', 'role': 'inspector'}
}
