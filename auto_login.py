from app_new import app, db, User, Role
from flask import Flask, session
from flask_login import login_user
from werkzeug.security import check_password_hash

def auto_login():
    with app.app_context():
        # البحث عن مفتش الاختبار
        inspector = User.query.filter_by(username='inspector_test').first()
        
        if not inspector:
            print("مفتش الاختبار غير موجود")
            return
        
        # تسجيل الدخول كمفتش
        with app.test_request_context():
            login_user(inspector)
            print(f"تم تسجيل الدخول كمفتش: {inspector.username}")
            print(f"معرف المستخدم في الجلسة: {session.get('_user_id')}")

if __name__ == "__main__":
    auto_login()
