# ترقية قاعدة البيانات

إذا واجهت خطأ `sqlalchemy.exc.OperationalError: no such column: educational_level.is_active` أو أي خطأ مشابه يتعلق بأعمدة مفقودة، فهذا يعني أن هيكل قاعدة البيانات الحالية لا يتطابق مع النماذج المحدثة.

## الخيار 1: إعادة إنشاء قاعدة البيانات (سيتم فقدان جميع البيانات)

1. احذف ملف قاعدة البيانات الحالي `ta9affi.db`
2. أعد تشغيل التطبيق لإنشاء قاعدة بيانات جديدة:
   ```
   python run.py
   ```
3. قم بإضافة البيانات التجريبية:
   ```
   python seed_data.py
   ```

## الخيار 2: ترقية قاعدة البيانات الحالية (يحافظ على البيانات الموجودة)

1. قم بتشغيل سكريبت ترقية قاعدة البيانات:
   ```
   python upgrade_db.py
   ```
2. أعد تشغيل التطبيق:
   ```
   python run.py
   ```

## ملاحظات هامة

- إذا كنت تستخدم قاعدة بيانات فارغة أو جديدة، فمن الأفضل استخدام الخيار 1.
- إذا كان لديك بيانات مهمة في قاعدة البيانات الحالية، فاستخدم الخيار 2.
- قد تحتاج إلى تشغيل `seed_level_databases()` بعد الترقية لإضافة قواعد البيانات المنفصلة:
  ```python
  from seed_data import seed_level_databases
  from app import app
  
  with app.app_context():
      seed_level_databases()
  ```
