import json

# تحميل البيانات الحالية
try:
    with open('materials_data.json', 'r', encoding='utf-8') as file:
        materials_data = json.load(file)
except Exception as e:
    print(f"خطأ في قراءة ملف البيانات: {str(e)}")
    materials_data = {
        "اللغة العربية": {}
    }

# إضافة ميادين جديدة وموارد معرفية
if "اللغة العربية" in materials_data:
    # إضافة ميدان "التعبير الشفهي ( صيغ )"
    materials_data["اللغة العربية"]["التعبير الشفهي ( صيغ )"] = [
        "ألفاظ النسبة",
        "ظروف الزمان",
        "التشبيه بـ : كـ",
        "العطف",
        "ظروف المكان",
        "المفعول المطلق",
        "سـ / سوف",
        "صفات الشخصية",
        "ما ، إن",
        "أفعال دالة على الحركة",
        "التشبيه بـ : كأن",
        "ألفاظ النسبة",
        "التفضيل",
        "بينما ...إذا بـ....",
        "التعجب: ما أفعل...!",
        "ظروف المكان",
        "لولا.... لـ...",
        "الاستثناء بـ: إلا - سوى",
        "الاستدراك بـ : لكن",
        "التفضيل",
        "أفعال الحركة",
        "جنوباشمالا/ - شرقا / غربا",
        "ظروف المكان"
    ]
    
    # إضافة ميدان "التعبير الشفهي ( إنتاج)"
    materials_data["اللغة العربية"]["التعبير الشفهي ( إنتاج)"] = [
        "صديقتي حورية",
        "البائع الصغير",
        "جدي",
        "جيران الأمس و اليوم",
        "رامي و المعلم الجديد",
        "الجار الجديد",
        "بعيدا عن أرضي",
        "الأمير عبد القادر",
        "مطار مصطفى بن بولعيد",
        "نظافة المدرسة",
        "العيش في المدينة",
        "المسكن الشمسي",
        "هدية النخلة",
        "معاناة مريض",
        "أكتشف اللعبة",
        "أنامل معطرة",
        "البرنوس",
        "الطاسيلي ناجر",
        "الغواصة الاِستكشافية",
        "من عصر الحجارة إلى عصر الحاسوب",
        "القلم عبر التاريخ",
        "جمال بلادي",
        "صحراءنا الجميلة"
    ]
    
    # إضافة ميدان "التعبير الكتابي (أركب)"
    materials_data["اللغة العربية"]["التعبير الكتابي (أركب)"] = [
        "يتصرف في الأحداث (الإطار الزماني) من حيث ترتيبها باستعمال أدوات الربط: و، ف، ثم",
        "يتصرف في الأحداث (الإطار المكاني) من حيث ترتيبها باستعمال أدوات الربط: و، ف، ثم",
        "يتصرف في النص بإغنائه بالوصف:ـ الوصف بالجملة الإسمية- الوصف بالنعت",
        "يتصرف في النص بإغنائه بالوصف: ـ الوصف بالحال ـ و الوصف بالمفعول المطلق",
        "يتصرف في النص بإغنائه بالحوار: ـالأفعال الدالة",
        "ينتج نصا سرديا مغنى بالوصف وبحوار: - العلاقة بين الشخصيات ـ وصف الشخصيات",
        "ينتج نصا سرديا مغنى بالوصف وبحوار : ـ وصف الأعمال والأقوال ـ وصف الإطار المكاني وألزماني",
        "إنتاج نص سردي مركب الأحداث، مغنى بالوصف.",
        "إنتاج نص سردي مركب الأحداث، مغنى بالوصف"
    ]
    
    # إضافة ميدان "إملاء"
    materials_data["اللغة العربية"]["إملاء"] = [
        "الضمائر المنفصلة",
        "التاء المفتوحة في الأفعال",
        "تصريف الفعل الماضي مع ضمائر المتكلم",
        "التاء المفتوحة في الأسماء",
        "تصريف الفعل الماضي مع ضمائر المخاطب و الغائب",
        "التاء المربوطة في الأسماء",
        "تصريف الماضي مع جميع الضمائر",
        "الهمزة المتوسطة على الألف",
        "تصريف المضارع مع ضمائر المتكلم و المخاطب",
        "الهمزة المتوسطة على الواو",
        "تصريف المضارع مع ضمائر الغائب",
        "الهمزة المتوسطة على النبرة",
        "تصريف فعل الأمر",
        "الهمزة في آخر الكلمة",
        "(المتطرفة)",
        "اسم الفاعل",
        "الأسماء الموصولة",
        "اسم المفعول",
        "الألف اللينة في الأفعال",
        "الاسم في المفرد والمثنى",
        "الألف اللينة في الاسماء",
        "المصدر",
        "الألف اللينة في الحروف",
        "الاسم في المفرد و جمع المذكر السالم"
    ]
    
    # إضافة ميدان "التراكيب النحوية"
    materials_data["اللغة العربية"]["التراكيب النحوية"] = [
        "أنواع الكلمة",
        "الفعل الماضي",
        "الجملة الفعلية",
        "الفاعل",
        "المفعول به",
        "الجملة الاسمية",
        "الصفة",
        "حروف الجر",
        "المضاف إليه",
        "فعل الأمر",
        "كان وأخواتها",
        "الاسم في الإفراد والتثنية",
        "الحال",
        "إن وأخواتها",
        "جمع المذكر السالم",
        "جمع المؤنث السالم",
        "جمع التكسير",
        "مراجعة",
        "مراجعة",
        "مراجعة",
        "الفعل الصحيح",
        "الفعل المعتل"
    ]
    
    # إضافة ميدان "الصرف"
    materials_data["اللغة العربية"]["الصرف"] = [
        "الضمائر المنفصلة",
        "التاء المفتوحة في الأفعال",
        "تصريف الفعل الماضي مع ضمائر المتكلم",
        "التاء المفتوحة في الأسماء",
        "تصريف الفعل الماضي مع ضمائر المخاطب و الغائب",
        "التاء المربوطة في الأسماء",
        "تصريف الماضي مع جميع الضمائر",
        "الهمزة المتوسطة على الألف",
        "تصريف المضارع مع ضمائر المتكلم و المخاطب",
        "الهمزة المتوسطة على الواو",
        "تصريف المضارع مع ضمائر الغائب",
        "الهمزة المتوسطة على النبرة",
        "تصريف فعل الأمر",
        "الهمزة في آخر الكلمة",
        "(المتطرفة)",
        "اسم الفاعل",
        "الأسماء الموصولة",
        "اسم المفعول",
        "الألف اللينة في الأفعال",
        "الاسم في المفرد والمثنى",
        "الألف اللينة في الاسماء",
        "المصدر",
        "الألف اللينة في الحروف",
        "الاسم في المفرد و جمع المذكر السالم"
    ]
    
    # إضافة ميدان "قراءة (اداء و فهم)"
    materials_data["اللغة العربية"]["قراءة (اداء و فهم)"] = [
        "مع عصاي في المدرسة",
        "ماسح الزجاج",
        "حفنة نقود",
        "التاجماعث",
        "المعلم الجديد",
        "بين جارين",
        "الحنين إلى الوطن",
        "الأمير عبد القادر",
        "الزائر العزيز",
        "رسالة الثعلب",
        "بيوتنا بين الأمس واليوم",
        "طاقة لا تنفذ",
        "قصة زيتونة",
        "مرض سامية",
        "لمن تهتف الحناجر",
        "أنامل من ذهب",
        "لباسنا الجميل",
        "القاص الطارقي",
        "مركبة الأعماق",
        "سالم والحاسوب",
        "بهية والقلم",
        "جولة في بلادي",
        "حكايات في حقيبتي"
    ]
    
    # إضافة ميدان "محفوظات"
    materials_data["اللغة العربية"]["محفوظات"] = [
        "الأمل الممكن",
        "صحوة بخيل",
        "- أمي",
        "- تاج الوفاء",
        "وطني",
        "يا أمي لا تبكي علي",
        "الضياء",
        "تغريدة العندليب",
        "التوازن الغذائي",
        "رياضة الأبدان",
        "الكتاب",
        "علبة الألوان",
        "تلفاز وحاسوب",
        "علماء المستقبل",
        "الحمامة المهاجرة",
        "الواحة",
        "الحمامة المهاجرة",
        "الواحة"
    ]

# حفظ البيانات المحدثة إلى ملف JSON
try:
    with open('materials_data_updated.json', 'w', encoding='utf-8') as file:
        json.dump(materials_data, file, ensure_ascii=False, indent=4)
    print("تم تحديث البيانات بنجاح وحفظها في ملف: materials_data_updated.json")
except Exception as e:
    print(f"خطأ في حفظ البيانات إلى ملف: {str(e)}")
