"""مزخرفات للتحقق من أدوار المستخدمين"""

from functools import wraps
from flask import flash, redirect, url_for
from flask_login import current_user
from app.models import Role

def admin_required(f):
    """
    مزخرفة للتحقق من أن المستخدم هو مدير
    """
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_admin():
            flash('غير مصرح لك بالوصول إلى هذه الصفحة.', 'danger')
            return redirect(url_for('main.dashboard'))
        return f(*args, **kwargs)
    return decorated_function

def inspector_required(f):
    """
    مزخرفة للتحقق من أن المستخدم هو مفتش
    """
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_inspector():
            flash('غير مصرح لك بالوصول إلى هذه الصفحة.', 'danger')
            return redirect(url_for('main.dashboard'))
        return f(*args, **kwargs)
    return decorated_function

def teacher_required(f):
    """
    مزخرفة للتحقق من أن المستخدم هو أستاذ
    """
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_teacher():
            flash('غير مصرح لك بالوصول إلى هذه الصفحة.', 'danger')
            return redirect(url_for('main.dashboard'))
        return f(*args, **kwargs)
    return decorated_function

def admin_or_inspector_required(f):
    """
    مزخرفة للتحقق من أن المستخدم هو مدير أو مفتش
    """
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not (current_user.is_admin() or current_user.is_inspector()):
            flash('غير مصرح لك بالوصول إلى هذه الصفحة.', 'danger')
            return redirect(url_for('main.dashboard'))
        return f(*args, **kwargs)
    return decorated_function

def inspector_or_teacher_required(f):
    """
    مزخرفة للتحقق من أن المستخدم هو مفتش أو أستاذ
    """
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not (current_user.is_inspector() or current_user.is_teacher()):
            flash('غير مصرح لك بالوصول إلى هذه الصفحة.', 'danger')
            return redirect(url_for('main.dashboard'))
        return f(*args, **kwargs)
    return decorated_function