from app_new import app, db, LevelDataEntry

def main():
    with app.app_context():
        # البحث عن مادة الرياضيات في السنة الرابعة ابتدائي
        math = LevelDataEntry.query.filter_by(database_id=4, entry_type='subject', name='الرياضيات').first()
        
        if math:
            # الحصول على الميادين
            domains = LevelDataEntry.query.filter_by(database_id=4, entry_type='domain', parent_id=math.id).all()
            
            print(f'عدد ميادين الرياضيات في السنة الرابعة ابتدائي: {len(domains)}')
            for domain in domains:
                print(f'- {domain.name}')
        else:
            print("لم يتم العثور على مادة الرياضيات في السنة الرابعة ابتدائي")

if __name__ == "__main__":
    main()
