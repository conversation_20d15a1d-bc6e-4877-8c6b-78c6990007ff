"""
إعدادات تطبيق Ta9affi
"""

import os
from datetime import timedelta

basedir = os.path.abspath(os.path.dirname(os.path.dirname(__file__)))

class Config:
    """
    الإعدادات الأساسية للتطبيق
    """
    # استخدام مفتاح سري قوي
    SECRET_KEY = os.environ.get('SECRET_KEY') or os.urandom(32)
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    NOTIFICATIONS_PER_PAGE = 10
    
    # إعدادات أمان الجلسة
    SESSION_COOKIE_SECURE = True  # تفعيل في بيئة الإنتاج فقط
    SESSION_COOKIE_HTTPONLY = True
    SESSION_COOKIE_SAMESITE = 'Lax'
    PERMANENT_SESSION_LIFETIME = timedelta(days=1)
    
    # إعدادات CSRF
    WTF_CSRF_ENABLED = True
    WTF_CSRF_TIME_LIMIT = 3600  # مدة صلاحية رمز CSRF بالثواني (ساعة واحدة)
    
    @staticmethod
    def init_app(app):
        pass

class DevelopmentConfig(Config):
    """
    إعدادات بيئة التطوير
    """
    DEBUG = True
    SQLALCHEMY_DATABASE_URI = os.environ.get('DEV_DATABASE_URL') or \
        'sqlite:///' + os.path.join(basedir, 'instance', 'ta9affi_dev.db')

class TestingConfig(Config):
    """
    إعدادات بيئة الاختبار
    """
    TESTING = True
    SQLALCHEMY_DATABASE_URI = os.environ.get('TEST_DATABASE_URL') or \
        'sqlite:///' + os.path.join(basedir, 'instance', 'ta9affi_test.db')

class ProductionConfig(Config):
    """
    إعدادات بيئة الإنتاج
    """
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or \
        'sqlite:///' + os.path.join(basedir, 'instance', 'ta9affi.db')

# قاموس الإعدادات
config = {
    'development': DevelopmentConfig,
    'testing': TestingConfig,
    'production': ProductionConfig,
    'default': DevelopmentConfig
}