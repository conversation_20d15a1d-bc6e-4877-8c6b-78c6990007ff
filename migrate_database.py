from app_new import app, db
import sqlite3
import os

def migrate_database():
    """
    تحديث هيكل قاعدة البيانات لإضافة الأعمدة الجديدة إلى جدول progress_entry
    """
    with app.app_context():
        # الحصول على مسار قاعدة البيانات
        db_uri = app.config['SQLALCHEMY_DATABASE_URI']

        # التحقق من مسار قاعدة البيانات
        if db_uri.startswith('sqlite:///'):
            db_path = db_uri.replace('sqlite:///', '')
            # إذا كان المسار نسبيًا، فهو في مجلد instance
            if not os.path.isabs(db_path):
                db_path = os.path.join('instance', db_path)
        else:
            print(f"قاعدة البيانات ليست من نوع SQLite: {db_uri}")
            return False

        if not os.path.exists(db_path):
            print(f"قاعدة البيانات غير موجودة في المسار: {db_path}")
            return False

        print(f"جاري تحديث قاعدة البيانات في المسار: {db_path}")

        try:
            # إنشاء اتصال مباشر بقاعدة البيانات
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()

            # التحقق من وجود الأعمدة الجديدة
            cursor.execute("PRAGMA table_info(progress_entry)")
            columns = [column[1] for column in cursor.fetchall()]

            # إضافة الأعمدة الجديدة إذا لم تكن موجودة
            if 'level_id' not in columns:
                print("إضافة عمود level_id")
                cursor.execute("ALTER TABLE progress_entry ADD COLUMN level_id INTEGER")

            if 'subject_id' not in columns:
                print("إضافة عمود subject_id")
                cursor.execute("ALTER TABLE progress_entry ADD COLUMN subject_id INTEGER")

            if 'domain_id' not in columns:
                print("إضافة عمود domain_id")
                cursor.execute("ALTER TABLE progress_entry ADD COLUMN domain_id INTEGER")

            if 'material_id' not in columns:
                print("إضافة عمود material_id")
                cursor.execute("ALTER TABLE progress_entry ADD COLUMN material_id INTEGER")

            # حفظ التغييرات
            conn.commit()
            conn.close()

            print("تم تحديث هيكل قاعدة البيانات بنجاح")
            return True

        except Exception as e:
            print(f"حدث خطأ أثناء تحديث قاعدة البيانات: {str(e)}")
            return False

if __name__ == "__main__":
    migrate_database()
