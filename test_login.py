#!/usr/bin/env python3
"""
اختبار تسجيل الدخول
"""

from app import create_app, db
from app.models import User

def test_login():
    """اختبار تسجيل الدخول للمستخدمين"""
    app = create_app()
    
    with app.app_context():
        # بيانات الاعتماد للاختبار
        test_credentials = [
            {'username': 'admin', 'password': 'Admin@1234'},
            {'username': 'inspector', 'password': 'Inspector#2024'},
            {'username': 'teacher', 'password': 'Teacher@2024'},
            {'username': 'admin', 'password': 'wrong_password'},  # كلمة مرور خاطئة
            {'username': 'nonexistent', 'password': 'any_password'}  # مستخدم غير موجود
        ]
        
        print("=== اختبار تسجيل الدخول ===\n")
        
        for cred in test_credentials:
            username = cred['username']
            password = cred['password']
            
            print(f"اختبار: {username} / {password}")
            
            # البحث عن المستخدم
            user = User.query.filter_by(username=username).first()
            
            if not user:
                print("❌ المستخدم غير موجود")
            elif not user.is_active:
                print("❌ الحساب معطل")
            elif not user.check_password(password):
                print("❌ كلمة المرور غير صحيحة")
            else:
                print(f"✅ تم تسجيل الدخول بنجاح - الدور: {user.role}")
            
            print("---")

if __name__ == '__main__':
    test_login()
