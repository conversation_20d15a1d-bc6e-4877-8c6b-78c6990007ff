# Ta9affi - نظام إدارة البرنامج السنوي للتدريس

نظام إدارة البرنامج السنوي للتدريس في التعليم الإبتدائي في الجزائر.

## الوصف

تطبيق ويب يسمح للإدارة العامة والمفتشين والأساتذة بمتابعة تقدم البرنامج السنوي للتدريس. يمكن للإدارة العامة أن تطلع على المفتشين، والمفتشين يمكنهم الإطلاع على مدى تقدم الأساتذة تحت إشرافهم في البرنامج السنوي لجميع المستويات في التعليم الإبتدائي.

## المميزات

- نظام تسجيل دخول متعدد المستويات (إدارة، مفتش، أستاذ)
- إدارة البرنامج السنوي للتدريس
- تتبع تقدم الأساتذة في تنفيذ البرنامج
- إدارة جدول التدريس الخاص بكل أستاذ
- نظام قوائم منسدلة متسلسلة (المستوى، المادة، الميدان، المواد المعرفية، الكفاءة المستهدفة)
- استيراد وتصدير البيانات بصيغة Excel
- واجهة مستخدم عصرية ومتجاوبة باستخدام Bootstrap

## متطلبات النظام

- Python 3.8+
- Flask
- SQLAlchemy
- Flask-Login
- Flask-WTF
- pandas
- openpyxl

## التثبيت

1. قم بتثبيت Python 3.8 أو أحدث
2. قم بإنشاء بيئة افتراضية:
   ```
   python -m venv venv
   ```
3. قم بتفعيل البيئة الافتراضية:
   - Windows: `venv\Scripts\activate`
   - Linux/Mac: `source venv/bin/activate`
4. قم بتثبيت المتطلبات:
   ```
   pip install -r requirements.txt
   ```

## تشغيل التطبيق

1. قم بتفعيل البيئة الافتراضية إذا لم تكن مفعلة بالفعل
2. قم بتهيئة قاعدة البيانات وإنشاء حساب مدير (اختياري):
   ```
   python cli.py init-db
   python cli.py create-admin
   ```
3. قم بإضافة البيانات التجريبية (اختياري):
   ```
   python cli.py create-test-data
   ```
4. قم بتشغيل التطبيق:
   ```
   python run.py
   ```
5. افتح المتصفح وانتقل إلى `http://localhost:5000`

## حسابات المستخدمين التجريبية

يمكنك استخدام الحسابات التالية للدخول إلى النظام بعد تشغيل ملف `seed_data.py`:

### حساب الإدارة
- اسم المستخدم: `admin`
- كلمة المرور: `admin123`

### حساب المفتش
- اسم المستخدم: `inspector`
- كلمة المرور: `inspector123`

### حساب الأستاذ
- اسم المستخدم: `teacher`
- كلمة المرور: `teacher123`

### حساب أستاذ آخر
- اسم المستخدم: `teacher2`
- كلمة المرور: `teacher123`

ملاحظة: تم ربط المفتش بالأستاذين في النظام، بحيث يمكن للمفتش متابعة تقدمهم في البرنامج السنوي.

## هيكل المشروع

```
Ta9affi/
├── app/                    # حزمة التطبيق الرئيسية
│   ├── __init__.py         # تهيئة التطبيق
│   ├── config.py           # إعدادات التطبيق
│   ├── models.py           # نماذج قاعدة البيانات
│   ├── blueprints/         # البلوبرنت (تنظيم المسارات)
│   │   ├── __init__.py
│   │   ├── admin.py        # مسارات المدير
│   │   ├── api.py          # واجهة برمجة التطبيق
│   │   ├── auth.py         # مسارات المصادقة
│   │   ├── inspector.py    # مسارات المفتش
│   │   ├── main.py         # المسارات الرئيسية
│   │   └── teacher.py      # مسارات الأستاذ
│   ├── static/             # الملفات الثابتة (CSS، JS، الصور)
│   ├── templates/          # قوالب HTML
│   └── utils/              # أدوات مساعدة
│       ├── __init__.py
│       ├── data.py         # وظائف التعامل مع البيانات
│       └── decorators.py   # مزخرفات للتحقق من الأدوار
├── cli.py                  # أوامر سطر الأوامر
├── run.py                  # ملف تشغيل التطبيق
├── requirements.txt        # متطلبات التطبيق
└── README.md               # توثيق المشروع
    └── manage_schedule.html # صفحة إدارة جدول التدريس
```

## المساهمة

نرحب بمساهماتكم! يرجى إرسال طلبات السحب أو فتح مشكلة لمناقشة التغييرات المقترحة.

## الترخيص

هذا المشروع مرخص بموجب رخصة MIT.
