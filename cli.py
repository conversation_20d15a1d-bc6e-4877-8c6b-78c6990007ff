"""أوامر سطر الأوامر لتطبيق Ta9affi"""

import click
import os
from app import create_app, db
from app.models import User, Role, EducationalLevel
from app.utils.data import migrate_data

app = create_app()

@click.group()
def cli():
    """أوامر سطر الأوامر لتطبيق Ta9affi"""
    pass

@cli.command('init-db')
def init_db():
    """تهيئة قاعدة البيانات وإنشاء الجداول"""
    with app.app_context():
        db.create_all()
        click.echo('تم إنشاء قاعدة البيانات بنجاح!')

@cli.command('create-admin')
@click.option('--username', prompt=True, help='اسم المستخدم للمدير')
@click.option('--email', prompt=True, help='البريد الإلكتروني للمدير')
@click.option('--password', prompt=True, hide_input=True, confirmation_prompt=True, help='كلمة المرور للمدير')
def create_admin(username, email, password):
    """إنشاء حساب مدير جديد"""
    with app.app_context():
        # التحقق من عدم وجود مستخدم بنفس اسم المستخدم أو البريد الإلكتروني
        existing_user = User.query.filter((User.username == username) | (User.email == email)).first()
        if existing_user:
            click.echo('اسم المستخدم أو البريد الإلكتروني مستخدم بالفعل.')
            return
        
        # إنشاء مستخدم جديد
        admin = User(username=username, email=email, role=Role.ADMIN)
        admin.set_password(password)
        
        db.session.add(admin)
        db.session.commit()
        
        click.echo(f'تم إنشاء حساب المدير {username} بنجاح!')

@cli.command('create-test-data')
def create_test_data():
    """إنشاء بيانات اختبار للتطبيق"""
    with app.app_context():
        # إنشاء مستخدمين للاختبار
        admin = User(username='admin', email='<EMAIL>', role=Role.ADMIN)
        admin.set_password('admin123')
        
        inspector = User(username='inspector', email='<EMAIL>', role=Role.INSPECTOR)
        inspector.set_password('inspector123')
        
        teacher = User(username='teacher', email='<EMAIL>', role=Role.TEACHER)
        teacher.set_password('teacher123')
        
        db.session.add_all([admin, inspector, teacher])
        
        # إنشاء مستويات تعليمية للاختبار
        primary = EducationalLevel(name='التعليم الابتدائي', database_prefix='primary')
        middle = EducationalLevel(name='التعليم المتوسط', database_prefix='middle')
        secondary = EducationalLevel(name='التعليم الثانوي', database_prefix='secondary')
        
        db.session.add_all([primary, middle, secondary])
        
        # ربط المفتش بالأستاذ
        inspector.supervised_teachers.append(teacher)
        
        db.session.commit()
        
        click.echo('تم إنشاء بيانات الاختبار بنجاح!')

@cli.command('migrate-old-db')
@click.option('--source', default='ta9affi.db', help='اسم قاعدة البيانات القديمة')
@click.option('--target', default='ta9affi_new.db', help='اسم قاعدة البيانات الجديدة')
def migrate_old_db(source, target):
    """ترحيل البيانات من قاعدة البيانات القديمة إلى الجديدة"""
    # التحقق من وجود قاعدة البيانات المصدر
    source_path = os.path.join(os.getcwd(), source)
    if not os.path.exists(source_path):
        click.echo(f'قاعدة البيانات المصدر {source} غير موجودة.')
        return
    
    # التحقق من وجود قاعدة البيانات الهدف
    target_path = os.path.join(os.getcwd(), target)
    if not os.path.exists(target_path):
        click.echo(f'قاعدة البيانات الهدف {target} غير موجودة.')
        return
    
    # ترحيل البيانات
    migrated_tables = migrate_data(source_path, target_path)
    
    click.echo(f'تم ترحيل البيانات بنجاح! الجداول المرحلة: {migrated_tables}')

if __name__ == '__main__':
    cli()