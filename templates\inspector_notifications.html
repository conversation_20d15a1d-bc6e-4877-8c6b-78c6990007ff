{% extends 'base.html' %}

{% block content %}
<div class="container-fluid">
    <h2 class="mt-4 mb-4">إدارة الإشعارات</h2>

    <div class="row">
        <div class="col-md-4">
            <div class="card mb-4">
                <div class="card-header">
                    <i class="fas fa-paper-plane me-1"></i>
                    إرسال إشعار جديد للأساتذة
                </div>
                <div class="card-body">
                    <form action="{{ url_for('inspector.send_notification_to_teachers') }}" method="post">
                        {{ csrf_token() }}
                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                        <input type="hidden" name="receiver_type" value="specific"/>
                        <div class="mb-3">
                            <label for="receiver_id" class="form-label">المستلم</label>
                            <select class="form-select" id="receiver_id" name="receiver_id" required>
                                <option value="">اختر الأستاذ</option>
                                {% for teacher in teachers %}
                                <option value="{{ teacher.id }}">{{ teacher.username }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="title" class="form-label">العنوان</label>
                            <input type="text" class="form-control" id="title" name="title" required>
                        </div>
                        <div class="mb-3">
                            <label for="message" class="form-label">الرسالة</label>
                            <textarea class="form-control" id="message" name="message" rows="5" required></textarea>
                        </div>
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-paper-plane me-1"></i> إرسال
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-md-8">
            <ul class="nav nav-tabs" id="notificationTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="received-tab" data-bs-toggle="tab" data-bs-target="#received" type="button" role="tab" aria-controls="received" aria-selected="true">
                        <i class="fas fa-inbox me-1"></i> الإشعارات الواردة
                        {% if admin_notifications.total > 0 %}
                        <span class="badge bg-primary">{{ admin_notifications.total }}</span>
                        {% endif %}
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="sent-tab" data-bs-toggle="tab" data-bs-target="#sent" type="button" role="tab" aria-controls="sent" aria-selected="false">
                        <i class="fas fa-paper-plane me-1"></i> الإشعارات المرسلة
                        {% if sent_notifications.total > 0 %}
                        <span class="badge bg-primary">{{ sent_notifications.total }}</span>
                        {% endif %}
                    </button>
                </li>
            </ul>

            <div class="tab-content" id="notificationTabsContent">
                <!-- الإشعارات الواردة من الإدارة -->
                <div class="tab-pane fade show active" id="received" role="tabpanel" aria-labelledby="received-tab">
                    <div class="card border-top-0">
                        <div class="card-body">
                            {% if admin_notifications.items %}
                            <div class="table-responsive">
                                <table class="table table-bordered table-hover">
                                    <thead class="table-light">
                                        <tr>
                                            <th>التاريخ</th>
                                            <th>المرسل</th>
                                            <th>العنوان</th>
                                            <th>الحالة</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for notification in admin_notifications.items %}
                                        <tr class="{% if not notification.is_read %}table-warning{% endif %}">
                                            <td>{{ notification.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                                            <td>{{ notification.sender.username }}</td>
                                            <td>
                                                <a href="#" data-bs-toggle="modal" data-bs-target="#receivedModal{{ notification.id }}">
                                                    {{ notification.title }}
                                                </a>
                                            </td>
                                            <td>
                                                {% if notification.is_read %}
                                                <span class="badge bg-success">مقروء</span>
                                                {% else %}
                                                <span class="badge bg-warning">غير مقروء</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                {% if not notification.is_read %}
                                                <button type="button" class="btn btn-sm btn-outline-primary mark-as-read-btn"
                                                        data-notification-id="{{ notification.id }}"
                                                        data-notification-type="admin_inspector">
                                                    <i class="fas fa-check me-1"></i> تحديد كمقروء
                                                </button>
                                                {% endif %}
                                            </td>
                                        </tr>

                                        <!-- Modal for notification details -->
                                        <div class="modal fade" id="receivedModal{{ notification.id }}" tabindex="-1" aria-labelledby="receivedModalLabel{{ notification.id }}" aria-hidden="true">
                                            <div class="modal-dialog">
                                                <div class="modal-content">
                                                    <div class="modal-header">
                                                        <h5 class="modal-title" id="receivedModalLabel{{ notification.id }}">{{ notification.title }}</h5>
                                                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                    </div>
                                                    <div class="modal-body">
                                                        <p><strong>التاريخ:</strong> {{ notification.created_at.strftime('%Y-%m-%d %H:%M') }}</p>
                                                        <p><strong>المرسل:</strong> {{ notification.sender.username }}</p>
                                                        <p><strong>الحالة:</strong>
                                                            {% if notification.is_read %}
                                                            <span class="badge bg-success">مقروء</span>
                                                            {% else %}
                                                            <span class="badge bg-warning">غير مقروء</span>
                                                            {% endif %}
                                                        </p>
                                                        <hr>
                                                        <div class="notification-message">
                                                            {{ notification.message|nl2br }}
                                                        </div>
                                                    </div>
                                                    <div class="modal-footer">
                                                        {% if not notification.is_read %}
                                                        <button type="button" class="btn btn-primary mark-as-read-btn"
                                                                data-notification-id="{{ notification.id }}"
                                                                data-notification-type="admin_inspector">
                                                            <i class="fas fa-check me-1"></i> تحديد كمقروء
                                                        </button>
                                                        {% endif %}
                                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>

                            <!-- Pagination for received notifications -->
                            <nav aria-label="Page navigation">
                                <ul class="pagination justify-content-center">
                                    {% if admin_notifications.has_prev %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('inspector.notifications', page=admin_notifications.prev_num) }}" aria-label="Previous">
                                            <span aria-hidden="true">&laquo;</span>
                                        </a>
                                    </li>
                                    {% else %}
                                    <li class="page-item disabled">
                                        <a class="page-link" href="#" aria-label="Previous">
                                            <span aria-hidden="true">&laquo;</span>
                                        </a>
                                    </li>
                                    {% endif %}

                                    {% for page_num in admin_notifications.iter_pages(left_edge=1, right_edge=1, left_current=1, right_current=2) %}
                                        {% if page_num %}
                                            {% if admin_notifications.page == page_num %}
                                            <li class="page-item active">
                                                <a class="page-link" href="{{ url_for('inspector.notifications', page=page_num) }}">{{ page_num }}</a>
                                            </li>
                                            {% else %}
                                            <li class="page-item">
                                                <a class="page-link" href="{{ url_for('inspector.notifications', page=page_num) }}">{{ page_num }}</a>
                                            </li>
                                            {% endif %}
                                        {% else %}
                                        <li class="page-item disabled">
                                            <a class="page-link" href="#">...</a>
                                        </li>
                                        {% endif %}
                                    {% endfor %}

                                    {% if admin_notifications.has_next %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('inspector.notifications', page=admin_notifications.next_num) }}" aria-label="Next">
                                            <span aria-hidden="true">&raquo;</span>
                                        </a>
                                    </li>
                                    {% else %}
                                    <li class="page-item disabled">
                                        <a class="page-link" href="#" aria-label="Next">
                                            <span aria-hidden="true">&raquo;</span>
                                        </a>
                                    </li>
                                    {% endif %}
                                </ul>
                            </nav>
                            {% else %}
                            <div class="alert alert-info">
                                لا توجد إشعارات واردة حتى الآن.
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <!-- الإشعارات المرسلة للأساتذة -->
                <div class="tab-pane fade" id="sent" role="tabpanel" aria-labelledby="sent-tab">
                    <div class="card border-top-0">
                        <div class="card-body">
                            {% if sent_notifications.items %}
                            <div class="table-responsive">
                                <table class="table table-bordered table-hover">
                                    <thead class="table-light">
                                        <tr>
                                            <th>التاريخ</th>
                                            <th>المستلم</th>
                                            <th>العنوان</th>
                                            <th>الحالة</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for notification in sent_notifications.items %}
                                        <tr>
                                            <td>{{ notification.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                                            <td>{{ notification.receiver.username }}</td>
                                            <td>
                                                <a href="#" data-bs-toggle="modal" data-bs-target="#sentModal{{ notification.id }}">
                                                    {{ notification.title }}
                                                </a>
                                            </td>
                                            <td>
                                                {% if notification.is_read %}
                                                <span class="badge bg-success">مقروء</span>
                                                {% else %}
                                                <span class="badge bg-warning">غير مقروء</span>
                                                {% endif %}
                                            </td>
                                        </tr>

                                        <!-- Modal for notification details -->
                                        <div class="modal fade" id="sentModal{{ notification.id }}" tabindex="-1" aria-labelledby="sentModalLabel{{ notification.id }}" aria-hidden="true">
                                            <div class="modal-dialog">
                                                <div class="modal-content">
                                                    <div class="modal-header">
                                                        <h5 class="modal-title" id="sentModalLabel{{ notification.id }}">{{ notification.title }}</h5>
                                                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                    </div>
                                                    <div class="modal-body">
                                                        <p><strong>التاريخ:</strong> {{ notification.created_at.strftime('%Y-%m-%d %H:%M') }}</p>
                                                        <p><strong>المستلم:</strong> {{ notification.receiver.username }}</p>
                                                        <p><strong>الحالة:</strong>
                                                            {% if notification.is_read %}
                                                            <span class="badge bg-success">مقروء</span>
                                                            {% else %}
                                                            <span class="badge bg-warning">غير مقروء</span>
                                                            {% endif %}
                                                        </p>
                                                        <hr>
                                                        <div class="notification-message">
                                                            {{ notification.message|nl2br }}
                                                        </div>
                                                    </div>
                                                    <div class="modal-footer">
                                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>

                            <!-- Pagination for sent notifications -->
                            <nav aria-label="Page navigation">
                                <ul class="pagination justify-content-center">
                                    {% if sent_notifications.has_prev %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('inspector.notifications', page=sent_notifications.prev_num) }}" aria-label="Previous">
                                            <span aria-hidden="true">&laquo;</span>
                                        </a>
                                    </li>
                                    {% else %}
                                    <li class="page-item disabled">
                                        <a class="page-link" href="#" aria-label="Previous">
                                            <span aria-hidden="true">&laquo;</span>
                                        </a>
                                    </li>
                                    {% endif %}

                                    {% for page_num in sent_notifications.iter_pages(left_edge=1, right_edge=1, left_current=1, right_current=2) %}
                                        {% if page_num %}
                                            {% if sent_notifications.page == page_num %}
                                            <li class="page-item active">
                                                <a class="page-link" href="{{ url_for('inspector.notifications', page=page_num) }}">{{ page_num }}</a>
                                            </li>
                                            {% else %}
                                            <li class="page-item">
                                                <a class="page-link" href="{{ url_for('inspector.notifications', page=page_num) }}">{{ page_num }}</a>
                                            </li>
                                            {% endif %}
                                        {% else %}
                                        <li class="page-item disabled">
                                            <a class="page-link" href="#">...</a>
                                        </li>
                                        {% endif %}
                                    {% endfor %}

                                    {% if sent_notifications.has_next %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('inspector.notifications', page=sent_notifications.next_num) }}" aria-label="Next">
                                            <span aria-hidden="true">&raquo;</span>
                                        </a>
                                    </li>
                                    {% else %}
                                    <li class="page-item disabled">
                                        <a class="page-link" href="#" aria-label="Next">
                                            <span aria-hidden="true">&raquo;</span>
                                        </a>
                                    </li>
                                    {% endif %}
                                </ul>
                            </nav>
                            {% else %}
                            <div class="alert alert-info">
                                لا توجد إشعارات مرسلة حتى الآن.
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // معالجة أزرار "تحديد كمقروء"
        document.querySelectorAll('.mark-as-read-btn').forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();

                const notificationId = this.getAttribute('data-notification-id');
                const notificationType = this.getAttribute('data-notification-type');
                const buttonElement = this;

                // تأكيد من المستخدم
                if (confirm('هل تريد تحديد هذا الإشعار كمقروء؟')) {
                    // تعطيل الزر أثناء المعالجة
                    buttonElement.disabled = true;
                    buttonElement.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i> جاري التحديث...';

                    // إرسال طلب AJAX
                    fetch(`/mark_notification_read/${notificationType}/${notificationId}`, {
                        method: 'GET',
                        headers: {
                            'X-Requested-With': 'XMLHttpRequest'
                        }
                    })
                    .then(response => {
                        if (response.ok) {
                            // إعادة تحميل الصفحة بعد نجاح العملية
                            setTimeout(() => {
                                window.location.reload();
                            }, 500);
                        } else {
                            throw new Error('فشل في تحديث الإشعار');
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        alert('حدث خطأ أثناء تحديث الإشعار. يرجى المحاولة مرة أخرى.');

                        // إعادة تفعيل الزر
                        buttonElement.disabled = false;
                        buttonElement.innerHTML = '<i class="fas fa-check me-1"></i> تحديد كمقروء';
                    });
                }
            });
        });

        console.log('تم تحميل صفحة إشعارات المفتش');
    });
</script>
{% endblock %}
