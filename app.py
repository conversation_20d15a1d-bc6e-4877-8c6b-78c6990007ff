from flask import Flask, render_template, request, redirect, url_for, flash, jsonify
from flask_sqlalchemy import SQLAlchemy
from flask_login import Login<PERSON>anager, UserMixin, login_user, login_required, logout_user, current_user
import os
import pandas as pd
from datetime import datetime

# Initialize Flask app
app = Flask(__name__)
app.config['SECRET_KEY'] = 'your-secret-key'
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///ta9affi.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# Initialize database
db = SQLAlchemy(app)

# Initialize login manager
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'

# Import routes after initializing app to avoid circular imports
from routes import *

# Import models to ensure they are registered with SQLAlchemy
from models import User, Role, EducationalLevel, Subject, Domain, KnowledgeMaterial, Competency, Schedule, ProgressEntry, LevelDatabase, LevelDataEntry

if __name__ == '__main__':
    with app.app_context():
        # Create all tables
        db.create_all()
        print("Database tables created successfully!")
    app.run(debug=True)
