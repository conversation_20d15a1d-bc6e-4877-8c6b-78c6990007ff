# حل مشكلة قاعدة البيانات

## المشكلة

```
OperationalError
sqlalchemy.exc.OperationalError: (sqlite3.OperationalError) no such column: educational_level.is_active
[SQL: SELECT educational_level.id AS educational_level_id, educational_level.name AS educational_level_name, educational_level.is_active AS educational_level_is_active, educational_level.database_prefix AS educational_level_database_prefix 
FROM educational_level]
```

## الحل

اتبع الخطوات التالية بالترتيب:

### 1. أغلق التطبيق

تأكد من إغلاق التطبيق تمامًا. إذا كان التطبيق قيد التشغيل، فقم بإيقافه بالضغط على `Ctrl+C` في نافذة الأوامر.

### 2. احذف قاعدة البيانات يدويًا

1. انتقل إلى مجلد المشروع:
   ```
   cd c:\Users\<USER>\Documents\augment-projects\Ta9affi
   ```

2. احذف ملف قاعدة البيانات:
   ```
   del ta9affi.db
   ```

### 3. أعد بناء قاعدة البيانات

قم بتشغيل سكريبت إعادة بناء قاعدة البيانات:
```
python rebuild_db.py
```

هذا السكريبت سيقوم بما يلي:
- حذف قاعدة البيانات الحالية (إذا كانت موجودة)
- حذف مجلد البيانات (إذا كان موجودًا)
- إنشاء قاعدة بيانات جديدة
- إنشاء جميع الجداول بالهيكل الصحيح
- إضافة المستخدمين التجريبيين
- إضافة المستويات التعليمية
- إضافة قواعد البيانات المنفصلة
- إضافة بيانات تجريبية للمستوى الأول

### 4. تشغيل التطبيق

بعد الانتهاء من إعادة بناء قاعدة البيانات، قم بتشغيل التطبيق:
```
python run.py
```

### 5. تسجيل الدخول

قم بتسجيل الدخول باستخدام حساب الإدارة:
- اسم المستخدم: `admin`
- كلمة المرور: `admin123`

## إذا استمرت المشكلة

إذا استمرت المشكلة بعد اتباع الخطوات السابقة، فقد يكون هناك مشكلة في كيفية تعريف النماذج أو في كيفية استخدامها. في هذه الحالة، يمكنك تجربة الخطوات التالية:

1. تأكد من أن ملف `models.py` يحتوي على التعريفات الصحيحة للنماذج.
2. تأكد من أن ملف `app.py` يقوم باستيراد جميع النماذج قبل إنشاء قاعدة البيانات.
3. تأكد من أن ملف `routes.py` يستخدم النماذج بشكل صحيح.

## حسابات المستخدمين التجريبية

بعد إعادة بناء قاعدة البيانات، يمكنك استخدام حسابات المستخدمين التجريبية التالية:

### حساب الإدارة
- اسم المستخدم: `admin`
- كلمة المرور: `admin123`

### حساب المفتش
- اسم المستخدم: `inspector`
- كلمة المرور: `inspector123`

### حساب الأستاذ
- اسم المستخدم: `teacher`
- كلمة المرور: `teacher123`

### حساب أستاذ آخر
- اسم المستخدم: `teacher2`
- كلمة المرور: `teacher123`
