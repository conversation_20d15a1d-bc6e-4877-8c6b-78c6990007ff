from app_new import app, db, LevelDataEntry

def main():
    with app.app_context():
        # البحث عن مادة اللغة العربية في السنة الرابعة ابتدائي
        arabic = LevelDataEntry.query.filter_by(database_id=4, entry_type='subject', name='اللغة العربية').first()
        
        if arabic:
            # الحصول على الميادين
            domains = LevelDataEntry.query.filter_by(database_id=4, entry_type='domain', parent_id=arabic.id).all()
            
            print(f'عدد ميادين اللغة العربية في السنة الرابعة ابتدائي: {len(domains)}')
            for domain in domains:
                print(f'- {domain.name}')
        else:
            print("لم يتم العثور على مادة اللغة العربية في السنة الرابعة ابتدائي")

if __name__ == "__main__":
    main()
