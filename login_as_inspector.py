from app_new import app, db, User, Role
from flask_login import login_user
from werkzeug.security import generate_password_hash, check_password_hash

def login_as_inspector():
    with app.app_context():
        # البحث عن مفتش الاختبار
        inspector = User.query.filter_by(username='inspector_test').first()
        
        if not inspector:
            # إنشاء مفتش جديد إذا لم يكن موجودًا
            inspector = User(
                username='inspector_test',
                email='<EMAIL>',
                password=generate_password_hash('password'),
                role=Role.INSPECTOR
            )
            db.session.add(inspector)
            db.session.commit()
            print(f"تم إنشاء مفتش جديد: {inspector.username}")
        
        # طباعة معلومات المفتش
        print(f"معلومات المفتش:")
        print(f"الاسم: {inspector.username}")
        print(f"البريد الإلكتروني: {inspector.email}")
        print(f"الدور: {inspector.role}")
        
        # طباعة الأساتذة تحت الإشراف
        supervised_teachers = inspector.supervised_teachers.all()
        print(f"عدد الأساتذة تحت الإشراف: {len(supervised_teachers)}")
        
        for i, teacher in enumerate(supervised_teachers, 1):
            print(f"{i}. {teacher.username} ({teacher.email})")
        
        # إذا لم يكن هناك أساتذة تحت الإشراف، قم بإنشاء بعضهم
        if len(supervised_teachers) == 0:
            from create_test_data import create_test_data
            create_test_data()
            print("تم إنشاء بيانات اختبار جديدة")
            
            # إعادة استدعاء الأساتذة بعد إنشائهم
            supervised_teachers = inspector.supervised_teachers.all()
            print(f"عدد الأساتذة تحت الإشراف بعد الإنشاء: {len(supervised_teachers)}")

if __name__ == "__main__":
    login_as_inspector()
