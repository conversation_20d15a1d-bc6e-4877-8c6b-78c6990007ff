{% extends 'base.html' %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-lg-7">
        <div class="card shadow-lg border-0 rounded-lg mt-5">
            <div class="card-header bg-primary text-white">
                <h3 class="text-center font-weight-light my-2">إنشاء حساب جديد</h3>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ url_for('auth.register') }}">
                    {{ form.hidden_tag() }}
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="form-floating mb-3 mb-md-0">
                                {{ form.username(class="form-control", placeholder="اسم المستخدم") }}
                                <label for="{{ form.username.id }}">اسم المستخدم</label>
                                {% if form.username.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.username.errors %}
                                    <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-floating">
                                {{ form.email(class="form-control", placeholder="البريد الإلكتروني") }}
                                <label for="{{ form.email.id }}">البريد الإلكتروني</label>
                                {% if form.email.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.email.errors %}
                                    <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="form-floating mb-3 mb-md-0">
                                {{ form.password(class="form-control", placeholder="كلمة المرور") }}
                                <label for="{{ form.password.id }}">كلمة المرور</label>
                                {% if form.password.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.password.errors %}
                                    <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-floating mb-3 mb-md-0">
                                {{ form.confirm_password(class="form-control", placeholder="تأكيد كلمة المرور") }}
                                <label for="{{ form.confirm_password.id }}">تأكيد كلمة المرور</label>
                                {% if form.confirm_password.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.confirm_password.errors %}
                                    <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    <div class="form-floating mb-3">
                        {{ form.role(class="form-select") }}
                        <label for="{{ form.role.id }}">الدور</label>
                        {% if form.role.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.role.errors %}
                            <span>{{ error }}</span>
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                    <div class="d-grid">
                        {{ form.submit(class="btn btn-primary btn-lg") }}
                    </div>
                </form>
            </div>
            <div class="card-footer text-center py-3">
                <div class="small">
                    <a href="{{ url_for('auth.login') }}">لديك حساب بالفعل؟ سجل دخولك!</a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}