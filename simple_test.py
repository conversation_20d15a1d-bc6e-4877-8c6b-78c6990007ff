#!/usr/bin/env python3
"""
إنشاء المستخدمين الافتراضيين
"""

try:
    print("بدء إنشاء المستخدمين...")

    from app import create_app, db
    print("تم استيراد التطبيق بنجاح")

    app = create_app()
    print("تم إنشاء التطبيق بنجاح")

    with app.app_context():
        print("داخل سياق التطبيق")

        from app.models import User
        print("تم استيراد نموذج User بنجاح")

        db.create_all()
        print("تم إنشاء قاعدة البيانات بنجاح")

        # إنشاء المستخدمين الافتراضيين
        users_data = [
            {'username': 'admin', 'email': '<EMAIL>', 'password': 'Admin@1234', 'role': 'admin'},
            {'username': 'inspector', 'email': '<EMAIL>', 'password': 'Inspector#2024', 'role': 'inspector'},
            {'username': 'teacher', 'email': '<EMAIL>', 'password': 'Teacher@2024', 'role': 'teacher'}
        ]

        for user_data in users_data:
            # التحقق من عدم وجود المستخدم
            existing_user = User.query.filter_by(username=user_data['username']).first()
            if not existing_user:
                user = User(
                    username=user_data['username'],
                    email=user_data['email'],
                    role=user_data['role']
                )
                user.set_password(user_data['password'])
                db.session.add(user)
                print(f"تم إنشاء المستخدم: {user_data['username']}")
            else:
                print(f"المستخدم موجود بالفعل: {user_data['username']}")

        db.session.commit()
        print("تم حفظ جميع المستخدمين بنجاح!")

        # عرض المستخدمين المتاحين
        print("\n=== المستخدمون المتاحون ===")
        users = User.query.all()
        for user in users:
            user_info = next((u for u in users_data if u['username'] == user.username), None)
            if user_info:
                print(f"اسم المستخدم: {user.username}")
                print(f"كلمة المرور: {user_info['password']}")
                print(f"الدور: {user.role}")
                print(f"نشط: {user.is_active}")
                print("---")

    print("انتهى إنشاء المستخدمين بنجاح!")

except Exception as e:
    print(f"خطأ: {str(e)}")
    import traceback
    traceback.print_exc()
