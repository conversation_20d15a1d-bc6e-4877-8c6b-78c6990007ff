# إعادة تعيين قاعدة البيانات

إذا كنت تواجه مشاكل مع قاعدة البيانات مثل:
- `sqlalchemy.exc.OperationalError: no such column: educational_level.is_active`
- `sqlalchemy.exc.InvalidRequestError: Attribute name 'metadata' is reserved`
- أو أي أخطاء أخرى متعلقة بهيكل قاعدة البيانات

فإن الحل الأكثر فعالية هو إعادة تعيين قاعدة البيانات بالكامل.

## كيفية إعادة تعيين قاعدة البيانات

1. قم بتشغيل سكريبت إعادة تعيين قاعدة البيانات:
   ```
   python reset_db.py
   ```

2. سيقوم السكريبت بما يلي:
   - حذف قاعدة البيانات الحالية
   - حذف مجلد البيانات
   - إنشاء قاعدة بيانات جديدة
   - إضافة البيانات التجريبية (المستخدمين، المستويات التعليمية، قواعد البيانات المنفصلة)

3. بعد الانتهاء، يمكنك تشغيل التطبيق:
   ```
   python run.py
   ```

## ملاحظة هامة

**تحذير**: سيؤدي تشغيل هذا السكريبت إلى حذف جميع البيانات الموجودة في قاعدة البيانات. تأكد من عمل نسخة احتياطية إذا كان لديك بيانات مهمة.

## حسابات المستخدمين التجريبية

بعد إعادة تعيين قاعدة البيانات، يمكنك استخدام حسابات المستخدمين التجريبية التالية:

### حساب الإدارة
- اسم المستخدم: `admin`
- كلمة المرور: `admin123`

### حساب المفتش
- اسم المستخدم: `inspector`
- كلمة المرور: `inspector123`

### حساب الأستاذ
- اسم المستخدم: `teacher`
- كلمة المرور: `teacher123`

### حساب أستاذ آخر
- اسم المستخدم: `teacher2`
- كلمة المرور: `teacher123`
