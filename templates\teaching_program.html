{% extends 'base.html' %}

{% block content %}
<div class="row">
    <div class="col-md-12 d-flex justify-content-between align-items-center mb-4">
        <h2>البرنامج السنوي للتدريس</h2>
        {% if current_user.role == 'admin' %}
        <div>
            <a href="{{ url_for('admin.cleanup_inactive_levels') }}" class="btn btn-danger" onclick="return confirm('هل أنت متأكد من حذف جميع المستويات التي ليس لديها قاعدة بيانات نشطة؟')">
                <i class="fas fa-trash me-1"></i> حذف المستويات غير النشطة
            </a>
        </div>
        {% endif %}
    </div>
</div>

<!-- بيانات قواعد البيانات للمستويات -->
<script>
    const levelDatabases = {
        {% for level_id, db_id in level_databases.items() %}
        "{{ level_id }}": "{{ db_id }}",
        {% endfor %}
    };
</script>

<div class="row">
    <div class="col-md-12">
        <div class="card mb-4">
            <div class="card-header">
                <i class="fas fa-search me-1"></i>
                اختيار الكفاءة المستهدفة
            </div>
            <div class="card-body">
                <form id="competencySelectionForm">
                    <div class="row mb-3">
                        <div class="col-md">
                            <label for="level" class="form-label">المستوى التعليمي</label>
                            <select class="form-select" id="level" required>
                                <option value="" selected disabled>اختر المستوى</option>
                                {% for level in levels %}
                                <option value="{{ level.id }}">{{ level.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md">
                            <label for="subject" class="form-label">المادة</label>
                            <select class="form-select" id="subject" disabled required>
                                <option value="" selected disabled>اختر المادة</option>
                            </select>
                        </div>
                        <div class="col-md">
                            <label for="domain" class="form-label">الميدان</label>
                            <select class="form-select" id="domain" disabled required>
                                <option value="" selected disabled>اختر الميدان</option>
                            </select>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md">
                            <label for="material" class="form-label">المادة المعرفية</label>
                            <select class="form-select" id="material" disabled required>
                                <option value="" selected disabled>اختر المادة المعرفية</option>
                            </select>
                        </div>
                        <div class="col-md">
                            <label for="competency" class="form-label">الكفاءة المستهدفة</label>
                            <select class="form-select" id="competency" disabled required>
                                <option value="" selected disabled>اختر الكفاءة</option>
                            </select>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<div class="row" id="competencyDetailsSection" style="display: none;">
    <div class="col-md-12">
        <div class="card mb-4">
            <div class="card-header">
                <i class="fas fa-clipboard-list me-1"></i>
                تفاصيل الكفاءة المستهدفة
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-8">
                        <h5 id="competencyTitle">عنوان الكفاءة</h5>
                        <p id="competencyDescription" class="lead">وصف الكفاءة المستهدفة</p>

                        <div class="mt-4">
                            <h6>تسجيل التقدم</h6>
                            <form id="progressForm" method="POST" action="{{ url_for('teacher.add_progress') }}">
                                {{ csrf_token() }}
                                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                                <input type="hidden" id="competency_id" name="competency_id">
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label for="date" class="form-label">التاريخ</label>
                                        <input type="date" class="form-control" id="date" name="date" required>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="status" class="form-label">الحالة</label>
                                        <select class="form-select" id="status" name="status" required>
                                            <option value="" selected disabled>اختر الحالة</option>
                                            <option value="completed">مكتمل</option>
                                            <option value="in_progress">قيد التنفيذ</option>
                                            <option value="planned">مخطط</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label for="notes" class="form-label">ملاحظات</label>
                                    <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
                                </div>
                                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-1"></i> حفظ التقدم
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card h-100">
                            <div class="card-header">
                                <i class="fas fa-history me-1"></i>
                                سجل التقدم
                            </div>
                            <div class="card-body">
                                <div id="progressHistory">
                                    <p class="text-center text-muted">لا يوجد سجل تقدم لهذه الكفاءة</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Get form elements
        const levelSelect = document.getElementById('level');
        const subjectSelect = document.getElementById('subject');
        const domainSelect = document.getElementById('domain');
        const materialSelect = document.getElementById('material');
        const competencySelect = document.getElementById('competency');
        const competencyDetailsSection = document.getElementById('competencyDetailsSection');
        const competencyTitle = document.getElementById('competencyTitle');
        const competencyDescription = document.getElementById('competencyDescription');
        const competencyIdInput = document.getElementById('competency_id');

        // Function to reset dropdown options
        function resetDropdown(dropdown) {
            // Eliminar todas las opciones excepto la primera (placeholder)
            dropdown.innerHTML = '<option value="">اختر...</option>';
            dropdown.disabled = true;
        }

        // Set current date as default for progress form
        document.getElementById('date').valueAsDate = new Date();

        // Level change event
        levelSelect.addEventListener('change', function() {
            // Reset subsequent dropdowns
            resetDropdown(subjectSelect);
            resetDropdown(domainSelect);
            resetDropdown(materialSelect);
            resetDropdown(competencySelect);
            competencyDetailsSection.style.display = 'none';

            // Check if the level has a database
            const levelId = this.value;
            if (!levelDatabases[levelId]) {
                alert('لا توجد قاعدة بيانات نشطة لهذا المستوى');
                return;
            }

            // Enable subject dropdown and fetch subjects
            subjectSelect.disabled = false;

            // Fetch subjects for selected level
            fetch(`/api/subjects/${levelId}`)
                .then(response => response.json())
                .then(data => {
                    // Populate subject dropdown
                    if (data.length === 0) {
                        alert('لا توجد مواد دراسية لهذا المستوى');
                        return;
                    }

                    // Limpiar opciones existentes antes de agregar nuevas
                    resetDropdown(subjectSelect);
                    subjectSelect.disabled = false;

                    data.forEach(subject => {
                        const option = document.createElement('option');
                        option.value = subject.id;
                        option.textContent = subject.name;
                        subjectSelect.appendChild(option);
                    });
                })
                .catch(error => {
                    console.error('Error fetching subjects:', error);
                    alert('حدث خطأ أثناء جلب المواد الدراسية');
                });
        });

        // Subject change event
        subjectSelect.addEventListener('change', function() {
            // Reset subsequent dropdowns
            resetDropdown(domainSelect);
            resetDropdown(materialSelect);
            resetDropdown(competencySelect);
            competencyDetailsSection.style.display = 'none';

            // Enable domain dropdown and fetch domains
            domainSelect.disabled = false;

            // Fetch domains for selected subject
            fetch(`/api/domains/${this.value}`)
                .then(response => response.json())
                .then(data => {
                    // Populate domain dropdown
                    if (data.length === 0) {
                        alert('لا توجد ميادين لهذه المادة');
                        return;
                    }

                    // Limpiar opciones existentes antes de agregar nuevas
                    resetDropdown(domainSelect);
                    domainSelect.disabled = false;

                    data.forEach(domain => {
                        const option = document.createElement('option');
                        option.value = domain.id;
                        option.textContent = domain.name;
                        domainSelect.appendChild(option);
                    });
                })
                .catch(error => {
                    console.error('Error fetching domains:', error);
                    alert('حدث خطأ أثناء جلب الميادين');
                });
        });

        // Domain change event
        domainSelect.addEventListener('change', function() {
            // Reset subsequent dropdowns
            resetDropdown(materialSelect);
            resetDropdown(competencySelect);
            competencyDetailsSection.style.display = 'none';

            // Enable material dropdown and fetch materials
            materialSelect.disabled = false;

            // Fetch knowledge materials for selected domain
            fetch(`/api/knowledge-materials/${this.value}`)
                .then(response => response.json())
                .then(data => {
                    // Populate material dropdown
                    if (data.length === 0) {
                        alert('لا توجد مواد معرفية لهذا الميدان');
                        return;
                    }

                    // Limpiar opciones existentes antes de agregar nuevas
                    resetDropdown(materialSelect);
                    materialSelect.disabled = false;

                    data.forEach(material => {
                        const option = document.createElement('option');
                        option.value = material.id;
                        option.textContent = material.name;
                        materialSelect.appendChild(option);
                    });
                })
                .catch(error => {
                    console.error('Error fetching materials:', error);
                    alert('حدث خطأ أثناء جلب المواد المعرفية');
                });
        });

        // Material change event
        materialSelect.addEventListener('change', function() {
            // Reset competency dropdown
            resetDropdown(competencySelect);
            competencyDetailsSection.style.display = 'none';

            // Enable competency dropdown and fetch competencies
            competencySelect.disabled = false;

            // Fetch competencies for selected material
            fetch(`/api/competencies/${this.value}`)
                .then(response => response.json())
                .then(data => {
                    // Populate competency dropdown
                    if (data.length === 0) {
                        alert('لا توجد كفاءات مستهدفة لهذه المادة المعرفية');
                        return;
                    }

                    // Limpiar opciones existentes antes de agregar nuevas
                    resetDropdown(competencySelect);
                    competencySelect.disabled = false;

                    data.forEach(competency => {
                        const option = document.createElement('option');
                        option.value = competency.id;

                        // استخدام الاسم إذا كان موجوداً، وإلا استخدام الوصف
                        const displayText = competency.name || competency.description;
                        option.textContent = displayText.substring(0, 50) + (displayText.length > 50 ? '...' : '');
                        option.setAttribute('data-description', competency.description || '');
                        competencySelect.appendChild(option);
                    });
                })
                .catch(error => {
                    console.error('Error fetching competencies:', error);
                    alert('حدث خطأ أثناء جلب الكفاءات المستهدفة');
                });
        });

        // Competency change event
        competencySelect.addEventListener('change', function() {
            // Show competency details section
            competencyDetailsSection.style.display = 'block';

            // Get selected option
            const selectedOption = this.options[this.selectedIndex];

            // Update competency details
            competencyTitle.textContent = `${levelSelect.options[levelSelect.selectedIndex].text} - ${subjectSelect.options[subjectSelect.selectedIndex].text}`;
            competencyDescription.textContent = selectedOption.getAttribute('data-description') || '';
            competencyIdInput.value = this.value;

            // Fetch progress history for this competency
            // This would be implemented in a real application
            // For now, we'll just show a placeholder
        });

        // Helper function to reset dropdown
        function resetDropdown(dropdown) {
            dropdown.innerHTML = '<option value="" selected disabled>اختر</option>';
            dropdown.disabled = true;
        }
    });
</script>
{% endblock %}
{% endblock %}
