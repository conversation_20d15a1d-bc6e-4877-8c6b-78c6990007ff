"""وظائف مساعدة للتعامل مع البيانات والتصدير/الاستيراد"""

import pandas as pd
import os
from datetime import datetime
from app.models import LevelDatabase, LevelDataEntry
from app import db

def export_database_to_excel(database_id):
    """
    تصدير قاعدة بيانات إلى ملف Excel
    
    Args:
        database_id: معرف قاعدة البيانات
        
    Returns:
        file_path: مسار الملف المصدر
    """
    # جلب قاعدة البيانات
    database = LevelDatabase.query.get_or_404(database_id)
    
    # جلب بيانات قاعدة البيانات
    entries = LevelDataEntry.query.filter_by(database_id=database_id).all()
    
    # تحويل البيانات إلى DataFrame
    data = []
    for entry in entries:
        data.append({
            'id': entry.id,
            'entry_type': entry.entry_type,
            'parent_id': entry.parent_id,
            'name': entry.name,
            'description': entry.description,
            'order_num': entry.order_num,
            'is_active': entry.is_active,
            'extra_data': entry.extra_data,
            'created_at': entry.created_at,
            'updated_at': entry.updated_at
        })
    
    df = pd.DataFrame(data)
    
    # إنشاء مجلد للتصدير إذا لم يكن موجوداً
    export_dir = os.path.join(os.getcwd(), 'exports')
    os.makedirs(export_dir, exist_ok=True)
    
    # إنشاء اسم الملف
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    file_name = f"{database.name}_{timestamp}.xlsx"
    file_path = os.path.join(export_dir, file_name)
    
    # تصدير البيانات إلى ملف Excel
    df.to_excel(file_path, index=False)
    
    return file_path

def import_database_from_excel(file_path, database_id):
    """
    استيراد بيانات من ملف Excel إلى قاعدة بيانات
    
    Args:
        file_path: مسار ملف Excel
        database_id: معرف قاعدة البيانات
        
    Returns:
        count: عدد العناصر المستوردة
    """
    # جلب قاعدة البيانات
    database = LevelDatabase.query.get_or_404(database_id)
    
    # قراءة ملف Excel
    df = pd.read_excel(file_path)
    
    # استيراد البيانات
    count = 0
    for _, row in df.iterrows():
        # التحقق من وجود العنصر
        existing_entry = None
        if 'id' in row and not pd.isna(row['id']):
            existing_entry = LevelDataEntry.query.filter_by(id=row['id'], database_id=database_id).first()
        
        if existing_entry:
            # تحديث العنصر الموجود
            existing_entry.entry_type = row['entry_type']
            existing_entry.parent_id = row['parent_id'] if not pd.isna(row['parent_id']) else None
            existing_entry.name = row['name']
            existing_entry.description = row['description'] if not pd.isna(row['description']) else None
            existing_entry.order_num = row['order_num'] if not pd.isna(row['order_num']) else 0
            existing_entry.is_active = row['is_active'] if not pd.isna(row['is_active']) else True
            existing_entry.extra_data = row['extra_data'] if not pd.isna(row['extra_data']) else None
            existing_entry.updated_at = datetime.utcnow()
        else:
            # إنشاء عنصر جديد
            new_entry = LevelDataEntry(
                database_id=database_id,
                entry_type=row['entry_type'],
                parent_id=row['parent_id'] if not pd.isna(row['parent_id']) else None,
                name=row['name'],
                description=row['description'] if not pd.isna(row['description']) else None,
                order_num=row['order_num'] if not pd.isna(row['order_num']) else 0,
                is_active=row['is_active'] if not pd.isna(row['is_active']) else True,
                extra_data=row['extra_data'] if not pd.isna(row['extra_data']) else None
            )
            db.session.add(new_entry)
        
        count += 1
    
    db.session.commit()
    
    return count

def migrate_data(source_db_path, target_db_path, tables=None):
    """
    ترحيل البيانات من قاعدة بيانات إلى أخرى
    
    Args:
        source_db_path: مسار قاعدة البيانات المصدر
        target_db_path: مسار قاعدة البيانات الهدف
        tables: قائمة الجداول المراد ترحيلها (إذا كانت None، يتم ترحيل جميع الجداول)
        
    Returns:
        migrated_tables: قائمة الجداول التي تم ترحيلها
    """
    import sqlite3
    
    # الاتصال بقاعدة البيانات المصدر
    source_conn = sqlite3.connect(source_db_path)
    source_cursor = source_conn.cursor()
    
    # الاتصال بقاعدة البيانات الهدف
    target_conn = sqlite3.connect(target_db_path)
    target_cursor = target_conn.cursor()
    
    # الحصول على قائمة الجداول في قاعدة البيانات المصدر
    source_cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
    all_tables = [row[0] for row in source_cursor.fetchall()]
    
    # تحديد الجداول المراد ترحيلها
    tables_to_migrate = tables if tables else all_tables
    
    # ترحيل البيانات
    migrated_tables = []
    for table in tables_to_migrate:
        if table in all_tables:
            try:
                # الحصول على هيكل الجدول
                source_cursor.execute(f"PRAGMA table_info({table})")
                columns = [row[1] for row in source_cursor.fetchall()]
                
                # الحصول على البيانات
                source_cursor.execute(f"SELECT * FROM {table}")
                rows = source_cursor.fetchall()
                
                # إدراج البيانات في قاعدة البيانات الهدف
                for row in rows:
                    placeholders = ', '.join(['?' for _ in range(len(row))])
                    columns_str = ', '.join(columns)
                    target_cursor.execute(f"INSERT OR REPLACE INTO {table} ({columns_str}) VALUES ({placeholders})", row)
                
                target_conn.commit()
                migrated_tables.append(table)
            except Exception as e:
                print(f"خطأ في ترحيل الجدول {table}: {str(e)}")
    
    # إغلاق الاتصالات
    source_conn.close()
    target_conn.close()
    
    return migrated_tables