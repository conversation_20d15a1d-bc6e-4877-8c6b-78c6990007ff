{% extends 'base.html' %}

{% block content %}
<div class="container-fluid">
    <h2 class="mt-4 mb-4">الإشعارات</h2>

    <div class="row">
        <div class="col-md-12">
            <div class="card mb-4">
                <div class="card-header">
                    <i class="fas fa-inbox me-1"></i>
                    الإشعارات الواردة من المفتشين
                </div>
                <div class="card-body">
                    {% if notifications.items %}
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>التاريخ</th>
                                    <th>المرسل</th>
                                    <th>العنوان</th>
                                    <th>الأولوية</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for notification in notifications.items %}
                                <tr class="{% if not notification.is_read %}table-warning{% endif %}">
                                    <td>{{ notification.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                                    <td>{{ notification.sender.username }}</td>
                                    <td>
                                        <a href="#" data-bs-toggle="modal" data-bs-target="#notificationModal{{ notification.id }}">
                                            {{ notification.title }}
                                        </a>
                                    </td>
                                    <td>
                                        {% if notification.priority == 'urgent' %}
                                        <span class="badge bg-danger">عاجلة</span>
                                        {% elif notification.priority == 'high' %}
                                        <span class="badge bg-warning">عالية</span>
                                        {% else %}
                                        <span class="badge bg-info">عادية</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if notification.is_read %}
                                        <span class="badge bg-success">مقروء</span>
                                        {% else %}
                                        <span class="badge bg-warning">غير مقروء</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if not notification.is_read %}
                                        <button type="button" class="btn btn-sm btn-outline-primary mark-as-read-btn"
                                                data-notification-id="{{ notification.id }}"
                                                data-notification-type="inspector_teacher">
                                            <i class="fas fa-check me-1"></i> تحديد كمقروء
                                        </button>
                                        {% endif %}
                                    </td>
                                </tr>

                                <!-- Modal for notification details -->
                                <div class="modal fade" id="notificationModal{{ notification.id }}" tabindex="-1" aria-labelledby="notificationModalLabel{{ notification.id }}" aria-hidden="true">
                                    <div class="modal-dialog">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h5 class="modal-title" id="notificationModalLabel{{ notification.id }}">{{ notification.title }}</h5>
                                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                            </div>
                                            <div class="modal-body">
                                                <p><strong>التاريخ:</strong> {{ notification.created_at.strftime('%Y-%m-%d %H:%M') }}</p>
                                                <p><strong>المرسل:</strong> {{ notification.sender.username }}</p>
                                                <p><strong>الأولوية:</strong>
                                                    {% if notification.priority == 'urgent' %}
                                                    <span class="badge bg-danger">عاجلة</span>
                                                    {% elif notification.priority == 'high' %}
                                                    <span class="badge bg-warning">عالية</span>
                                                    {% else %}
                                                    <span class="badge bg-info">عادية</span>
                                                    {% endif %}
                                                </p>
                                                <p><strong>الحالة:</strong>
                                                    {% if notification.is_read %}
                                                    <span class="badge bg-success">مقروء</span>
                                                    {% else %}
                                                    <span class="badge bg-warning">غير مقروء</span>
                                                    {% endif %}
                                                </p>
                                                <hr>
                                                <div class="notification-message">
                                                    {{ notification.message|nl2br }}
                                                </div>
                                            </div>
                                            <div class="modal-footer">
                                                {% if not notification.is_read %}
                                                <button type="button" class="btn btn-primary mark-as-read-btn"
                                                        data-notification-id="{{ notification.id }}"
                                                        data-notification-type="inspector_teacher">
                                                    <i class="fas fa-check me-1"></i> تحديد كمقروء
                                                </button>
                                                {% endif %}
                                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <nav aria-label="Page navigation">
                        <ul class="pagination justify-content-center">
                            {% if notifications.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('teacher.notifications', page=notifications.prev_num) }}" aria-label="Previous">
                                    <span aria-hidden="true">&laquo;</span>
                                </a>
                            </li>
                            {% else %}
                            <li class="page-item disabled">
                                <a class="page-link" href="#" aria-label="Previous">
                                    <span aria-hidden="true">&laquo;</span>
                                </a>
                            </li>
                            {% endif %}

                            {% for page_num in notifications.iter_pages(left_edge=1, right_edge=1, left_current=1, right_current=2) %}
                                {% if page_num %}
                                    {% if notifications.page == page_num %}
                                    <li class="page-item active">
                                        <a class="page-link" href="{{ url_for('teacher.notifications', page=page_num) }}">{{ page_num }}</a>
                                    </li>
                                    {% else %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('teacher.notifications', page=page_num) }}">{{ page_num }}</a>
                                    </li>
                                    {% endif %}
                                {% else %}
                                <li class="page-item disabled">
                                    <a class="page-link" href="#">...</a>
                                </li>
                                {% endif %}
                            {% endfor %}

                            {% if notifications.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('teacher.notifications', page=notifications.next_num) }}" aria-label="Next">
                                    <span aria-hidden="true">&raquo;</span>
                                </a>
                            </li>
                            {% else %}
                            <li class="page-item disabled">
                                <a class="page-link" href="#" aria-label="Next">
                                    <span aria-hidden="true">&raquo;</span>
                                </a>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% else %}
                    <div class="alert alert-info">
                        لا توجد إشعارات واردة حتى الآن.
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // معالجة أزرار "تحديد كمقروء"
        document.querySelectorAll('.mark-as-read-btn').forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();

                const notificationId = this.getAttribute('data-notification-id');
                const notificationType = this.getAttribute('data-notification-type');
                const buttonElement = this;

                // تأكيد من المستخدم
                if (confirm('هل تريد تحديد هذا الإشعار كمقروء؟')) {
                    // تعطيل الزر أثناء المعالجة
                    buttonElement.disabled = true;
                    buttonElement.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i> جاري التحديث...';

                    // إرسال طلب AJAX
                    fetch(`/mark_notification_read/${notificationType}/${notificationId}`, {
                        method: 'GET',
                        headers: {
                            'X-Requested-With': 'XMLHttpRequest'
                        }
                    })
                    .then(response => {
                        if (response.ok) {
                            // إعادة تحميل الصفحة بعد نجاح العملية
                            setTimeout(() => {
                                window.location.reload();
                            }, 500);
                        } else {
                            throw new Error('فشل في تحديث الإشعار');
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        alert('حدث خطأ أثناء تحديث الإشعار. يرجى المحاولة مرة أخرى.');

                        // إعادة تفعيل الزر
                        buttonElement.disabled = false;
                        buttonElement.innerHTML = '<i class="fas fa-check me-1"></i> تحديد كمقروء';
                    });
                }
            });
        });

        // تحسين استقرار النوافذ المنبثقة
        const modals = document.querySelectorAll('.modal');
        modals.forEach(modal => {
            // منع إغلاق النافذة عند النقر داخلها
            modal.addEventListener('click', function(e) {
                e.stopPropagation();
            });

            // إضافة تأخير قصير قبل إظهار النافذة
            modal.addEventListener('show.bs.modal', function() {
                setTimeout(() => {
                    this.style.display = 'block';
                }, 50);
            });

            // تحسين إغلاق النافذة
            modal.addEventListener('hidden.bs.modal', function() {
                this.style.display = 'none';
            });
        });

        console.log('تم تحميل صفحة إشعارات الأستاذ');
    });
</script>
{% endblock %}
