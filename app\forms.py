from flask_wtf import FlaskForm
from wtforms import <PERSON>Field, PasswordField, SelectField, BooleanField, SubmitField
from wtforms.validators import DataRequired, Email, Length, EqualTo, ValidationError, Regexp
from app.models import User

class RegistrationForm(FlaskForm):
    username = StringField('اسم المستخدم', validators=[
        DataRequired(message='يرجى إدخال اسم المستخدم'),
        Length(min=4, max=25, message='يجب أن يكون اسم المستخدم بين 4 و 25 حرفًا')
    ])
    email = StringField('البريد الإلكتروني', validators=[
        DataRequired(message='يرجى إدخال البريد الإلكتروني'),
        Email(message='يرجى إدخال بريد إلكتروني صحيح'),
        Length(min=6, max=50, message='يجب أن يكون البريد الإلكتروني بين 6 و 50 حرفًا')
    ])
    password = PasswordField('كلمة المرور', validators=[
        DataRequired(message='يرجى إدخال كلمة المرور'),
        Length(min=8, message='يجب أن تكون كلمة المرور 8 أحرف على الأقل'),
        Regexp(r'^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]',
               message='يجب أن تحتوي كلمة المرور على حرف كبير وحرف صغير ورقم ورمز خاص')
    ])
    confirm_password = PasswordField('تأكيد كلمة المرور', validators=[
        DataRequired(message='يرجى تأكيد كلمة المرور'),
        EqualTo('password', message='كلمتا المرور غير متطابقتين')
    ])
    role = SelectField('الدور', choices=[
        ('teacher', 'أستاذ'),
        ('inspector', 'مفتش'),
        ('admin', 'إدارة')
    ], validators=[DataRequired(message='يرجى اختيار الدور')])
    submit = SubmitField('إنشاء حساب')

    def validate_username(self, username):
        user = User.query.filter_by(username=username.data).first()
        if user:
            raise ValidationError('اسم المستخدم موجود بالفعل، يرجى اختيار اسم آخر')

    def validate_email(self, email):
        user = User.query.filter_by(email=email.data).first()
        if user:
            raise ValidationError('البريد الإلكتروني مستخدم بالفعل، يرجى استخدام بريد آخر')