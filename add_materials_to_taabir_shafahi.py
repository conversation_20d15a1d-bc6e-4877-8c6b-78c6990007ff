from app_new import app, db, LevelDataEntry

def add_materials_to_domain(level_db_id, subject_name, domain_name, materials_list):
    """
    إضافة موارد معرفية إلى ميدان محدد
    
    المعلمات:
    level_db_id: معرف قاعدة بيانات المستوى
    subject_name: اسم المادة الدراسية
    domain_name: اسم الميدان
    materials_list: قائمة الموارد المعرفية المراد إضافتها
    """
    with app.app_context():
        try:
            # البحث عن المادة الدراسية
            subject = LevelDataEntry.query.filter_by(
                database_id=level_db_id,
                entry_type='subject',
                name=subject_name
            ).first()
            
            if not subject:
                print(f"خطأ: لم يتم العثور على المادة '{subject_name}' في قاعدة البيانات {level_db_id}")
                return False
            
            # البحث عن الميدان
            domain = LevelDataEntry.query.filter_by(
                database_id=level_db_id,
                entry_type='domain',
                parent_id=subject.id,
                name=domain_name
            ).first()
            
            if not domain:
                print(f"خطأ: لم يتم العثور على الميدان '{domain_name}' في المادة '{subject_name}'")
                return False
            
            # إضافة الموارد المعرفية
            added_count = 0
            for material_name in materials_list:
                # التحقق من وجود المورد المعرفي
                existing_material = LevelDataEntry.query.filter_by(
                    database_id=level_db_id,
                    entry_type='material',
                    parent_id=domain.id,
                    name=material_name
                ).first()
                
                if existing_material:
                    print(f"المورد المعرفي '{material_name}' موجود بالفعل في الميدان '{domain_name}'")
                    continue
                
                # إضافة المورد المعرفي
                material = LevelDataEntry(
                    database_id=level_db_id,
                    entry_type='material',
                    parent_id=domain.id,
                    name=material_name,
                    description=f"مورد معرفي في ميدان {domain_name}",
                    is_active=True
                )
                db.session.add(material)
                added_count += 1
            
            db.session.commit()
            print(f"تم إضافة {added_count} مورد معرفي إلى الميدان '{domain_name}' في المادة '{subject_name}'")
            return True
            
        except Exception as e:
            db.session.rollback()
            print(f"حدث خطأ أثناء إضافة الموارد المعرفية: {str(e)}")
            return False

# قائمة الموارد المعرفية لميدان "التعبير الشفهي" في مادة "اللغة العربية"
taabir_shafahi_materials = [
    "صديقتي حورية",
    "البائع الصغير",
    "جدي",
    "جيران الأمس و اليوم",
    "رامي و المعلم الجديد",
    "الجار الجديد",
    "بعيدا عن أرضي",
    "الأمير عبد القادر",
    "مطار مصطفى بن بولعيد",
    "نظافة المدرسة",
    "العيش في المدينة",
    "المسكن الشمسي",
    "هدية النخلة",
    "معاناة مريض",
    "أكتشف اللعبة",
    "أنامل معطرة",
    "البرنوس",
    "الطاسيلي ناجر",
    "الغواصة الاِستكشافية",
    "من عصر الحجارة إلى عصر الحاسوب",
    "القلم عبر التاريخ",
    "جمال بلادي",
    "صحراءنا الجميلة"
]

if __name__ == "__main__":
    # إضافة الموارد المعرفية إلى ميدان "التعبير الشفهي" في مادة "اللغة العربية" للسنة الرابعة ابتدائي
    add_materials_to_domain(4, "اللغة العربية", "التعبير الشفهي", taabir_shafahi_materials)
