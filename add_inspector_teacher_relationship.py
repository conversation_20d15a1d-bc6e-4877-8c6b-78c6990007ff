#!/usr/bin/env python3
"""
سكريبت لإضافة العلاقة بين المفتش والأستاذ في قاعدة البيانات
"""

from app import create_app, db
from app.models import User, Role

def add_inspector_teacher_relationship():
    """إضافة العلاقة بين المفتش والأستاذ"""
    app = create_app()
    
    with app.app_context():
        # البحث عن المفتش
        inspector = User.query.filter_by(username='inspector', role=Role.INSPECTOR).first()
        if not inspector:
            print("❌ المفتش غير موجود!")
            return False
        
        # البحث عن الأستاذ
        teacher = User.query.filter_by(username='teacher', role=Role.TEACHER).first()
        if not teacher:
            print("❌ الأستاذ غير موجود!")
            return False
        
        print(f"✅ تم العثور على المفتش: {inspector.username} (ID: {inspector.id})")
        print(f"✅ تم العثور على الأستاذ: {teacher.username} (ID: {teacher.id})")
        
        # التحقق من وجود العلاقة
        if teacher in inspector.supervised_teachers.all():
            print("⚠️ العلاقة موجودة بالفعل!")
            return True
        
        # إضافة العلاقة
        try:
            inspector.supervised_teachers.append(teacher)
            db.session.commit()
            print("✅ تم إضافة العلاقة بنجاح!")
            
            # التحقق من العلاقة
            supervised_teachers = inspector.supervised_teachers.all()
            print(f"📊 عدد الأساتذة تحت إشراف {inspector.username}: {len(supervised_teachers)}")
            
            for i, t in enumerate(supervised_teachers, 1):
                print(f"   {i}. {t.username} ({t.email})")
            
            return True
            
        except Exception as e:
            print(f"❌ خطأ في إضافة العلاقة: {str(e)}")
            db.session.rollback()
            return False

def check_all_relationships():
    """فحص جميع العلاقات الموجودة"""
    app = create_app()
    
    with app.app_context():
        # جلب جميع المفتشين
        inspectors = User.query.filter_by(role=Role.INSPECTOR).all()
        print(f"📋 عدد المفتشين في النظام: {len(inspectors)}")
        
        for inspector in inspectors:
            supervised_teachers = inspector.supervised_teachers.all()
            print(f"\n👤 المفتش: {inspector.username}")
            print(f"   📊 عدد الأساتذة تحت الإشراف: {len(supervised_teachers)}")
            
            if supervised_teachers:
                for i, teacher in enumerate(supervised_teachers, 1):
                    print(f"      {i}. {teacher.username} ({teacher.email})")
            else:
                print("      ⚠️ لا يوجد أساتذة تحت الإشراف")

def create_additional_teachers():
    """إنشاء أساتذة إضافيين للاختبار"""
    app = create_app()
    
    with app.app_context():
        additional_teachers = [
            {
                'username': 'teacher2',
                'email': '<EMAIL>',
                'password': 'Teacher@2024',
                'role': Role.TEACHER
            },
            {
                'username': 'teacher3',
                'email': '<EMAIL>',
                'password': 'Teacher@2024',
                'role': Role.TEACHER
            }
        ]
        
        for teacher_data in additional_teachers:
            existing_teacher = User.query.filter_by(username=teacher_data['username']).first()
            if not existing_teacher:
                teacher = User(
                    username=teacher_data['username'],
                    email=teacher_data['email'],
                    role=teacher_data['role']
                )
                teacher.set_password(teacher_data['password'])
                db.session.add(teacher)
                print(f"✅ تم إنشاء الأستاذ: {teacher_data['username']}")
            else:
                print(f"⚠️ الأستاذ {teacher_data['username']} موجود بالفعل")
        
        db.session.commit()
        print("✅ تم إنشاء الأساتذة الإضافيين")

def assign_all_teachers_to_inspector():
    """ربط جميع الأساتذة بالمفتش"""
    app = create_app()
    
    with app.app_context():
        inspector = User.query.filter_by(username='inspector', role=Role.INSPECTOR).first()
        if not inspector:
            print("❌ المفتش غير موجود!")
            return
        
        teachers = User.query.filter_by(role=Role.TEACHER).all()
        print(f"📋 عدد الأساتذة في النظام: {len(teachers)}")
        
        added_count = 0
        for teacher in teachers:
            if teacher not in inspector.supervised_teachers.all():
                inspector.supervised_teachers.append(teacher)
                added_count += 1
                print(f"✅ تم ربط الأستاذ {teacher.username} بالمفتش")
            else:
                print(f"⚠️ الأستاذ {teacher.username} مرتبط بالفعل")
        
        if added_count > 0:
            db.session.commit()
            print(f"✅ تم ربط {added_count} أستاذ جديد بالمفتش")
        else:
            print("ℹ️ جميع الأساتذة مرتبطين بالفعل")

if __name__ == "__main__":
    print("🔧 إدارة العلاقات بين المفتشين والأساتذة")
    print("=" * 50)
    
    # فحص العلاقات الحالية
    print("\n1️⃣ فحص العلاقات الحالية:")
    check_all_relationships()
    
    # إنشاء أساتذة إضافيين
    print("\n2️⃣ إنشاء أساتذة إضافيين:")
    create_additional_teachers()
    
    # ربط جميع الأساتذة بالمفتش
    print("\n3️⃣ ربط جميع الأساتذة بالمفتش:")
    assign_all_teachers_to_inspector()
    
    # فحص العلاقات النهائية
    print("\n4️⃣ فحص العلاقات النهائية:")
    check_all_relationships()
    
    print("\n✅ تم الانتهاء من إعداد العلاقات!")
