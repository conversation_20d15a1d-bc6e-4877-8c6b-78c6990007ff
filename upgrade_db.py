"""
سكريبت لترقية قاعدة البيانات
يضيف الأعمدة الجديدة إلى الجداول الموجودة
"""

from app import app, db
import sqlite3
import os

def upgrade_database():
    """ترقية قاعدة البيانات بإضافة الأعمدة الجديدة"""
    db_path = os.path.join(app.root_path, 'ta9affi.db')
    
    if not os.path.exists(db_path):
        print("قاعدة البيانات غير موجودة!")
        return
    
    # إنشاء اتصال بقاعدة البيانات
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # التحقق من وجود الأعمدة الجديدة في جدول المستويات التعليمية
    cursor.execute("PRAGMA table_info(educational_level)")
    columns = [column[1] for column in cursor.fetchall()]
    
    # إضافة عمود is_active إذا لم يكن موجوداً
    if 'is_active' not in columns:
        print("إضافة عمود is_active إلى جدول educational_level...")
        cursor.execute("ALTER TABLE educational_level ADD COLUMN is_active BOOLEAN DEFAULT 1")
    
    # إضافة عمود database_prefix إذا لم يكن موجوداً
    if 'database_prefix' not in columns:
        print("إضافة عمود database_prefix إلى جدول educational_level...")
        cursor.execute("ALTER TABLE educational_level ADD COLUMN database_prefix VARCHAR(20)")
    
    # إنشاء جداول قواعد البيانات المنفصلة إذا لم تكن موجودة
    print("إنشاء جداول قواعد البيانات المنفصلة...")
    
    # جدول قواعد البيانات
    cursor.execute("""
    CREATE TABLE IF NOT EXISTS level_database (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        level_id INTEGER NOT NULL,
        name VARCHAR(100) NOT NULL,
        file_path VARCHAR(255) NOT NULL,
        is_active BOOLEAN DEFAULT 1,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (level_id) REFERENCES educational_level (id)
    )
    """)
    
    # جدول بيانات المستويات
    cursor.execute("""
    CREATE TABLE IF NOT EXISTS level_data_entry (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        database_id INTEGER NOT NULL,
        entry_type VARCHAR(50) NOT NULL,
        parent_id INTEGER,
        name VARCHAR(255) NOT NULL,
        description TEXT,
        order_num INTEGER DEFAULT 0,
        is_active BOOLEAN DEFAULT 1,
        extra_data TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (database_id) REFERENCES level_database (id)
    )
    """)
    
    # حفظ التغييرات
    conn.commit()
    conn.close()
    
    print("تمت ترقية قاعدة البيانات بنجاح!")

if __name__ == "__main__":
    with app.app_context():
        upgrade_database()
