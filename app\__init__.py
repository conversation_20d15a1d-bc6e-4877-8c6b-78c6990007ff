"""
تهيئة تطبيق Ta9affi
"""

from flask import Flask, request
from flask_sqlalchemy import SQLAlchemy
from flask_login import LoginManager
from flask_wtf.csrf import CSRFProtect
from flask_talisman import Talisman
from flask_limiter import Limiter
from flask_limiter.util import get_remote_address
import os

# تهيئة المكونات
db = SQLAlchemy()
login_manager = LoginManager()
csrf = CSRFProtect()
talisman = Talisman()

from app.commands import init_default_users

def create_app(config_class=Config):
    """
    إنشاء وتهيئة تطبيق Flask
    
    Args:
        config_name (str): اسم التكوين المراد استخدامه
        
    Returns:
        Flask: تطبيق Flask مهيأ
    """
    # إنشاء تطبيق Flask
    app = Flask(__name__, 
                template_folder='../templates',
                static_folder='../static')
    
    # تحميل الإعدادات
    from app.config import config
    app.config.from_object(config[config_name])
    
    # تهيئة قاعدة البيانات
    db.init_app(app)
    
    # تهيئة مدير تسجيل الدخول
    login_manager.init_app(app)
    login_manager.login_view = 'auth.login'
    
    # تهيئة حماية CSRF
    csrf.init_app(app)
    
    # تهيئة Talisman لإضافة رؤوس HTTP الأمنية
    csp = {
        'default-src': '\'self\'',
        'script-src': [
            '\'self\'',
            'https://cdn.jsdelivr.net',
            'https://cdnjs.cloudflare.com',
        ],
        'style-src': [
            '\'self\'',
            'https://cdn.jsdelivr.net',
            'https://cdnjs.cloudflare.com',
            'https://fonts.googleapis.com',
        ],
        'font-src': [
            '\'self\'',
            'https://cdn.jsdelivr.net',
            'https://cdnjs.cloudflare.com',
            'https://fonts.gstatic.com',
        ],
        'img-src': '\'self\'',
    }
    talisman.init_app(
        app,
        content_security_policy=csp,
        content_security_policy_nonce_in=['script-src'],
        force_https=False,  # تعيين إلى True في بيئة الإنتاج
        session_cookie_secure=app.config['SESSION_COOKIE_SECURE'],
        session_cookie_http_only=app.config['SESSION_COOKIE_HTTPONLY'],
        feature_policy={
            'geolocation': '\'none\'',
            'microphone': '\'none\'',
            'camera': '\'none\'',
        }
    )
    
    # تهيئة Limiter بعد تحميل الإعدادات
    limiter = Limiter(
        get_remote_address,
        app=app,
        storage_uri="memory://",
        strategy="fixed-window"
    )
    
    # إضافة فلتر nl2br لتنسيق النص في الرسائل
    @app.template_filter('nl2br')
    def nl2br(value):
        if value:
            return value.replace('\n', '<br>')
        return ''
    
    # تسجيل البلوبرنتات
    from app.blueprints.auth import auth_bp as auth_blueprint
    app.register_blueprint(auth_blueprint, url_prefix='/auth')
    
    from app.blueprints.main import main_bp as main_blueprint
    app.register_blueprint(main_blueprint)
    
    from app.blueprints.admin import admin_bp as admin_blueprint
    app.register_blueprint(admin_blueprint, url_prefix='/admin')
    
    from app.blueprints.inspector import inspector_bp as inspector_blueprint
    app.register_blueprint(inspector_blueprint, url_prefix='/inspector')
    
    from app.blueprints.teacher import teacher_bp as teacher_blueprint
    app.register_blueprint(teacher_blueprint, url_prefix='/teacher')
    
    from app.blueprints.api import api_bp as api_blueprint
    app.register_blueprint(api_blueprint, url_prefix='/api')
    
    # إنشاء مجلد للتصدير إذا لم يكن موجود
    os.makedirs(os.path.join(app.static_folder, 'exports'), exist_ok=True)
    
    # تسجيل أوامر CLI
    app.cli.add_command(init_default_users)
    
    return app
