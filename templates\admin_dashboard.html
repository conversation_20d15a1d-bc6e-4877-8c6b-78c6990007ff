{% extends 'base.html' %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <h2 class="mb-4">لوحة تحكم الإدارة</h2>
    </div>
</div>

<div class="row">
    <div class="col-xl-3 col-md-6">
        <div class="card bg-primary text-white mb-4">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>المفتشون</div>
                    <div class="h3">{{ inspectors|length }}</div>
                </div>
            </div>
            <div class="card-footer d-flex align-items-center justify-content-between">
                <a class="small text-white stretched-link" href="#" data-bs-toggle="modal" data-bs-target="#inspectorsModal">عرض التفاصيل</a>
                <div class="small text-white"><i class="fas fa-angle-left"></i></div>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6">
        <div class="card bg-success text-white mb-4">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>المستويات التعليمية</div>
                    <div class="h3">5</div>
                </div>
            </div>
            <div class="card-footer d-flex align-items-center justify-content-between">
                <a class="small text-white stretched-link" href="#" data-bs-toggle="modal" data-bs-target="#levelsModal">عرض التفاصيل</a>
                <div class="small text-white"><i class="fas fa-angle-left"></i></div>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6">
        <div class="card bg-warning text-white mb-4">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>المواد الدراسية</div>
                    <div class="h3">12</div>
                </div>
            </div>
            <div class="card-footer d-flex align-items-center justify-content-between">
                <a class="small text-white stretched-link" href="#" data-bs-toggle="modal" data-bs-target="#subjectsModal">عرض التفاصيل</a>
                <div class="small text-white"><i class="fas fa-angle-left"></i></div>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6">
        <div class="card bg-danger text-white mb-4">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>الكفاءات المستهدفة</div>
                    <div class="h3">150+</div>
                </div>
            </div>
            <div class="card-footer d-flex align-items-center justify-content-between">
                <a class="small text-white stretched-link" href="#" data-bs-toggle="modal" data-bs-target="#competenciesModal">عرض التفاصيل</a>
                <div class="small text-white"><i class="fas fa-angle-left"></i></div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-6">
        <div class="card mb-4">
            <div class="card-header">
                <i class="fas fa-user-graduate me-1"></i>
                المفتشون
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered table-hover">
                        <thead>
                            <tr>
                                <th>اسم المستخدم</th>
                                <th>البريد الإلكتروني</th>
                                <th>عدد الأساتذة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for inspector in inspectors %}
                            <tr>
                                <td>{{ inspector.username }}</td>
                                <td>{{ inspector.email }}</td>
                                <td>{{ inspector.supervised_teachers.count() }}</td>
                                <td>
                                    <a href="#" class="btn btn-sm btn-primary"><i class="fas fa-eye"></i></a>
                                    <a href="#" class="btn btn-sm btn-warning"><i class="fas fa-edit"></i></a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- مودال المفتشين -->
<div class="modal fade" id="inspectorsModal" tabindex="-1" aria-labelledby="inspectorsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="inspectorsModalLabel">تفاصيل المفتشين</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="table-responsive">
                    <table class="table table-bordered table-hover">
                        <thead>
                            <tr>
                                <th>اسم المستخدم</th>
                                <th>البريد الإلكتروني</th>
                                <th>عدد الأساتذة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for inspector in inspectors %}
                            <tr>
                                <td>{{ inspector.username }}</td>
                                <td>{{ inspector.email }}</td>
                                <td>{{ inspector.supervised_teachers.count() }}</td>
                                <td>
                                    <a href="{{ url_for('main.dashboard') }}" class="btn btn-sm btn-primary"><i class="fas fa-eye"></i> عرض</a>
                                    <a href="#" class="btn btn-sm btn-warning"><i class="fas fa-edit"></i> تعديل</a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>

<!-- مودال المستويات التعليمية -->
<div class="modal fade" id="levelsModal" tabindex="-1" aria-labelledby="levelsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title" id="levelsModalLabel">تفاصيل المستويات التعليمية</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="table-responsive">
                    <table class="table table-bordered table-hover">
                        <thead>
                            <tr>
                                <th>المستوى</th>
                                <th>عدد المواد</th>
                                <th>عدد الميادين</th>
                                <th>عدد الكفاءات</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>السنة الأولى ابتدائي</td>
                                <td>13</td>
                                <td>25</td>
                                <td>50</td>
                                <td>
                                    <a href="{{ url_for('admin.manage_databases') }}" class="btn btn-sm btn-primary"><i class="fas fa-eye"></i> عرض</a>
                                </td>
                            </tr>
                            <tr>
                                <td>السنة الثانية ابتدائي</td>
                                <td>13</td>
                                <td>25</td>
                                <td>50</td>
                                <td>
                                    <a href="{{ url_for('admin.manage_databases') }}" class="btn btn-sm btn-primary"><i class="fas fa-eye"></i> عرض</a>
                                </td>
                            </tr>
                            <tr>
                                <td>السنة الثالثة ابتدائي</td>
                                <td>13</td>
                                <td>25</td>
                                <td>50</td>
                                <td>
                                    <a href="{{ url_for('admin.manage_databases') }}" class="btn btn-sm btn-primary"><i class="fas fa-eye"></i> عرض</a>
                                </td>
                            </tr>
                            <tr>
                                <td>السنة الرابعة ابتدائي</td>
                                <td>13</td>
                                <td>25</td>
                                <td>50</td>
                                <td>
                                    <a href="{{ url_for('admin.manage_databases') }}" class="btn btn-sm btn-primary"><i class="fas fa-eye"></i> عرض</a>
                                </td>
                            </tr>
                            <tr>
                                <td>السنة الخامسة ابتدائي</td>
                                <td>13</td>
                                <td>25</td>
                                <td>50</td>
                                <td>
                                    <a href="{{ url_for('admin.manage_databases') }}" class="btn btn-sm btn-primary"><i class="fas fa-eye"></i> عرض</a>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>

<!-- مودال المواد الدراسية -->
<div class="modal fade" id="subjectsModal" tabindex="-1" aria-labelledby="subjectsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-warning text-white">
                <h5 class="modal-title" id="subjectsModalLabel">تفاصيل المواد الدراسية</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="table-responsive">
                    <table class="table table-bordered table-hover">
                        <thead>
                            <tr>
                                <th>المادة</th>
                                <th>المستوى</th>
                                <th>عدد الميادين</th>
                                <th>عدد الكفاءات</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>اللغة العربية</td>
                                <td>جميع المستويات</td>
                                <td>5</td>
                                <td>25</td>
                                <td>
                                    <a href="{{ url_for('admin.manage_databases') }}" class="btn btn-sm btn-primary"><i class="fas fa-eye"></i> عرض</a>
                                </td>
                            </tr>
                            <tr>
                                <td>الرياضيات</td>
                                <td>جميع المستويات</td>
                                <td>4</td>
                                <td>20</td>
                                <td>
                                    <a href="{{ url_for('admin.manage_databases') }}" class="btn btn-sm btn-primary"><i class="fas fa-eye"></i> عرض</a>
                                </td>
                            </tr>
                            <tr>
                                <td>التربية الإسلامية</td>
                                <td>جميع المستويات</td>
                                <td>3</td>
                                <td>15</td>
                                <td>
                                    <a href="{{ url_for('admin.manage_databases') }}" class="btn btn-sm btn-primary"><i class="fas fa-eye"></i> عرض</a>
                                </td>
                            </tr>
                            <tr>
                                <td>التربية العلمية والتكنولوجية</td>
                                <td>جميع المستويات</td>
                                <td>4</td>
                                <td>20</td>
                                <td>
                                    <a href="{{ url_for('admin.manage_databases') }}" class="btn btn-sm btn-primary"><i class="fas fa-eye"></i> عرض</a>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>

<!-- مودال الكفاءات المستهدفة -->
<div class="modal fade" id="competenciesModal" tabindex="-1" aria-labelledby="competenciesModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="competenciesModalLabel">تفاصيل الكفاءات المستهدفة</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i> يمكنك عرض الكفاءات المستهدفة من خلال صفحة قواعد البيانات.
                </div>
                <div class="text-center mt-3">
                    <a href="{{ url_for('admin.manage_databases') }}" class="btn btn-primary btn-lg">
                        <i class="fas fa-database"></i> الذهاب إلى قواعد البيانات
                    </a>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>

{% block extra_js %}
<script>
    // كود JavaScript للتعامل مع المودالات
    document.addEventListener('DOMContentLoaded', function() {
        // التعامل مع مودال المفتشين
        const inspectorsModal = document.getElementById('inspectorsModal');
        if (inspectorsModal) {
            inspectorsModal.addEventListener('show.bs.modal', function(event) {
                // يمكن إضافة كود لتحديث البيانات في المودال قبل عرضه
            });
        }

        // التعامل مع مودال المستويات
        const levelsModal = document.getElementById('levelsModal');
        if (levelsModal) {
            levelsModal.addEventListener('show.bs.modal', function(event) {
                // يمكن إضافة كود لتحديث البيانات في المودال قبل عرضه
            });
        }

        // التعامل مع مودال المواد
        const subjectsModal = document.getElementById('subjectsModal');
        if (subjectsModal) {
            subjectsModal.addEventListener('show.bs.modal', function(event) {
                // يمكن إضافة كود لتحديث البيانات في المودال قبل عرضه
            });
        }

        // التعامل مع مودال الكفاءات
        const competenciesModal = document.getElementById('competenciesModal');
        if (competenciesModal) {
            competenciesModal.addEventListener('show.bs.modal', function(event) {
                // يمكن إضافة كود لتحديث البيانات في المودال قبل عرضه
            });
        }
    });
</script>
{% endblock %}
{% endblock %}
