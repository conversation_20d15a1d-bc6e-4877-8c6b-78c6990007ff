/* Dark Mode Styles for Ta9affi application */

:root {
    --primary-color: #1976d2;
    --primary-hover: #1565c0;
    --light-bg: #f8f9fa;
    --light-text: #212529;
    --light-border: rgba(0, 0, 0, 0.125);
    --light-card-header: rgba(0, 0, 0, 0.03);
    --light-footer: rgba(0, 0, 0, 0.05);
    
    --dark-bg: #121212;
    --dark-secondary-bg: #1e1e1e;
    --dark-card-bg: #2d2d2d;
    --dark-text: #e0e0e0;
    --dark-border: rgba(255, 255, 255, 0.125);
    --dark-card-header: rgba(255, 255, 255, 0.05);
    --dark-footer: rgba(255, 255, 255, 0.05);
    --dark-input-bg: #2d2d2d;
    --dark-input-text: #e0e0e0;
    --dark-input-border: #444;
    --dark-dropdown-bg: #2d2d2d;
    --dark-dropdown-hover: #3d3d3d;
    --dark-table-header: #2d2d2d;
    --dark-table-border: #444;
    --dark-table-stripe: rgba(255, 255, 255, 0.05);
}

/* Dark Mode Styles */
body.dark-mode {
    background-color: var(--dark-bg);
    color: var(--dark-text);
}

/* Navbar */
body.dark-mode .navbar {
    background-color: var(--dark-secondary-bg) !important;
}

body.dark-mode .navbar-dark .navbar-nav .nav-link {
    color: var(--dark-text);
}

/* Cards */
body.dark-mode .card {
    background-color: var(--dark-card-bg);
    border-color: var(--dark-border);
}

body.dark-mode .card-header {
    background-color: var(--dark-secondary-bg);
    border-bottom-color: var(--dark-border);
}

/* Tables */
body.dark-mode .table {
    color: var(--dark-text);
    border-color: var(--dark-table-border);
}

body.dark-mode .table th {
    background-color: var(--dark-table-header);
    border-color: var(--dark-table-border);
}

body.dark-mode .table td {
    border-color: var(--dark-table-border);
}

body.dark-mode .table-striped tbody tr:nth-of-type(odd) {
    background-color: var(--dark-table-stripe);
}

/* Forms */
body.dark-mode .form-control,
body.dark-mode .form-select {
    background-color: var(--dark-input-bg);
    border-color: var(--dark-input-border);
    color: var(--dark-input-text);
}

body.dark-mode .form-control:focus,
body.dark-mode .form-select:focus {
    background-color: var(--dark-input-bg);
    color: var(--dark-input-text);
    border-color: var(--primary-color);
}

body.dark-mode .form-floating > label {
    color: var(--dark-input-text);
}

/* Dropdowns */
body.dark-mode .dropdown-menu {
    background-color: var(--dark-dropdown-bg);
    border-color: var(--dark-border);
}

body.dark-mode .dropdown-item {
    color: var(--dark-text);
}

body.dark-mode .dropdown-item:hover,
body.dark-mode .dropdown-item:focus {
    background-color: var(--dark-dropdown-hover);
    color: var(--dark-text);
}

body.dark-mode .dropdown-divider {
    border-top-color: var(--dark-border);
}

/* Alerts */
body.dark-mode .alert {
    background-color: var(--dark-secondary-bg);
    color: var(--dark-text);
    border-color: var(--dark-border);
}

/* Footer */
body.dark-mode footer {
    background-color: var(--dark-secondary-bg) !important;
    color: var(--dark-text);
}

body.dark-mode footer .text-dark {
    color: var(--dark-text) !important;
}

body.dark-mode footer .bg-light {
    background-color: var(--dark-secondary-bg) !important;
}

/* Modal */
body.dark-mode .modal-content {
    background-color: var(--dark-card-bg);
    border-color: var(--dark-border);
}

body.dark-mode .modal-header,
body.dark-mode .modal-footer {
    border-color: var(--dark-border);
}

/* List groups */
body.dark-mode .list-group-item {
    background-color: var(--dark-card-bg);
    border-color: var(--dark-border);
    color: var(--dark-text);
}

/* Tabs */
body.dark-mode .nav-tabs {
    border-bottom-color: var(--dark-border);
}

body.dark-mode .nav-tabs .nav-link.active {
    background-color: var(--dark-card-bg);
    border-color: var(--dark-border);
    color: var(--dark-text);
}

body.dark-mode .nav-tabs .nav-link:not(.active) {
    color: var(--dark-text);
}

/* Progress bars */
body.dark-mode .progress {
    background-color: var(--dark-secondary-bg);
}