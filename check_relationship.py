from app_new import app, db, User, Role, inspector_teacher

def check_relationship():
    with app.app_context():
        # البحث عن مفتش الاختبار
        inspector = User.query.filter_by(username='inspector_test').first()
        
        if not inspector:
            print("مفتش الاختبار غير موجود")
            return
        
        print(f"معلومات المفتش:")
        print(f"الاسم: {inspector.username}")
        print(f"البريد الإلكتروني: {inspector.email}")
        print(f"الدور: {inspector.role}")
        print(f"المعرف: {inspector.id}")
        
        # التحقق من العلاقة باستخدام العلاقة المباشرة
        supervised_teachers = inspector.supervised_teachers.all()
        print(f"عدد الأساتذة تحت الإشراف (باستخدام العلاقة المباشرة): {len(supervised_teachers)}")
        
        for i, teacher in enumerate(supervised_teachers, 1):
            print(f"{i}. {teacher.username} ({teacher.email}) - المعرف: {teacher.id}")
        
        # التحقق من العلاقة باستخدام استعلام SQL مباشر
        teachers_query = db.session.query(User).join(
            inspector_teacher,
            User.id == inspector_teacher.c.teacher_id
        ).filter(
            inspector_teacher.c.inspector_id == inspector.id,
            User.role == Role.TEACHER
        )
        sql_teachers = teachers_query.all()
        
        print(f"\nعدد الأساتذة تحت الإشراف (باستخدام استعلام SQL): {len(sql_teachers)}")
        
        for i, teacher in enumerate(sql_teachers, 1):
            print(f"{i}. {teacher.username} ({teacher.email}) - المعرف: {teacher.id}")
        
        # التحقق من جدول العلاقة مباشرة
        relationships = db.session.query(inspector_teacher).filter(
            inspector_teacher.c.inspector_id == inspector.id
        ).all()
        
        print(f"\nعدد العلاقات في جدول inspector_teacher: {len(relationships)}")
        
        for i, rel in enumerate(relationships, 1):
            teacher = User.query.get(rel.teacher_id)
            teacher_name = teacher.username if teacher else "غير موجود"
            print(f"{i}. المفتش: {inspector.id} - الأستاذ: {rel.teacher_id} ({teacher_name})")
        
        # إذا لم تكن هناك علاقات، قم بإنشائها
        if len(relationships) == 0:
            print("\nلا توجد علاقات. إنشاء علاقات جديدة...")
            
            # الحصول على جميع الأساتذة
            teachers = User.query.filter_by(role=Role.TEACHER).all()
            
            for teacher in teachers:
                # إضافة العلاقة
                stmt = inspector_teacher.insert().values(
                    inspector_id=inspector.id,
                    teacher_id=teacher.id
                )
                db.session.execute(stmt)
            
            db.session.commit()
            print("تم إنشاء العلاقات بنجاح")

if __name__ == "__main__":
    check_relationship()
