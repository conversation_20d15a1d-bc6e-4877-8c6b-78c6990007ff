from app_new import app, db, EducationalLevel, LevelDatabase, LevelDataEntry
import pandas as pd
import os

def reset_all_databases():
    """حذف جميع البيانات الحالية وإعادة إنشاء قواعد البيانات"""
    with app.app_context():
        try:
            # حذف جميع البيانات من جميع قواعد البيانات
            print("جاري حذف جميع البيانات من قواعد البيانات...")
            LevelDataEntry.query.delete()
            db.session.commit()
            print("تم حذف جميع البيانات بنجاح")
            
            # قائمة المواد الدراسية
            subjects = [
                {"name": "اللغة العربية", "description": "مادة اللغة العربية", "is_active": True},
                {"name": "الرياضيات", "description": "مادة الرياضيات", "is_active": True},
                {"name": "التربية الاسلامية", "description": "مادة التربية الإسلامية", "is_active": True},
                {"name": "الفرنسية", "description": "مادة اللغة الفرنسية", "is_active": True},
                {"name": "التربية العلمية", "description": "مادة التربية العلمية", "is_active": True},
                {"name": "التربية المدنية", "description": "مادة التربية المدنية", "is_active": True},
                {"name": "التربية الفنية /التشكيلية", "description": "مادة التربية الفنية والتشكيلية", "is_active": True},
                {"name": "التاريخ", "description": "مادة التاريخ", "is_active": True},
                {"name": "الجغرافيا", "description": "مادة الجغرافيا", "is_active": True},
                {"name": "التربية البدنية", "description": "مادة التربية البدنية", "is_active": True},
                {"name": "حفظ القرآن", "description": "مادة حفظ القرآن", "is_active": True},
                {"name": "الأمازيغية", "description": "مادة اللغة الأمازيغية", "is_active": True},
                {"name": "الإنجليزية", "description": "مادة اللغة الإنجليزية", "is_active": True},
            ]
            
            # قائمة الميادين
            domains_data = [
                # ميادين اللغة العربية
                {"name": "فهم المنطوق", "description": "ميدان في اللغة العربية", "parent_name": "اللغة العربية"},
                {"name": "التعبير الشفهي", "description": "ميدان في اللغة العربية", "parent_name": "اللغة العربية"},
                {"name": "فهم المكتوب (ألعاب قرائية)", "description": "ميدان في اللغة العربية", "parent_name": "اللغة العربية"},
                {"name": "التعبير الشفهي ( صيغ )", "description": "ميدان في اللغة العربية", "parent_name": "اللغة العربية"},
                {"name": "التعبير الشفهي ( إنتاج)", "description": "ميدان في اللغة العربية", "parent_name": "اللغة العربية"},
                {"name": "التعبير الكتابي (أركب)", "description": "ميدان في اللغة العربية", "parent_name": "اللغة العربية"},
                {"name": "التعبير الكتابي (إنتاج كتابي)", "description": "ميدان في اللغة العربية", "parent_name": "اللغة العربية"},
                {"name": "استكشاف الحرف", "description": "ميدان في اللغة العربية", "parent_name": "اللغة العربية"},
                {"name": "كتابة الحرف", "description": "ميدان في اللغة العربية", "parent_name": "اللغة العربية"},
                {"name": "إملاء", "description": "ميدان في اللغة العربية", "parent_name": "اللغة العربية"},
                {"name": "التراكيب النحوية", "description": "ميدان في اللغة العربية", "parent_name": "اللغة العربية"},
                {"name": "الصرف", "description": "ميدان في اللغة العربية", "parent_name": "اللغة العربية"},
                {"name": "قراءة اجمالية", "description": "ميدان في اللغة العربية", "parent_name": "اللغة العربية"},
                {"name": "قراءة (اداء و فهم)", "description": "ميدان في اللغة العربية", "parent_name": "اللغة العربية"},
                {"name": "محفوظات", "description": "ميدان في اللغة العربية", "parent_name": "اللغة العربية"},
                {"name": "المشاريع", "description": "ميدان في اللغة العربية", "parent_name": "اللغة العربية"},
                {"name": "تطبيقات اللغة", "description": "ميدان في اللغة العربية", "parent_name": "اللغة العربية"},
                {"name": "تصحيح التعبير الكتابي", "description": "ميدان في اللغة العربية", "parent_name": "اللغة العربية"},
                {"name": "معالجة اللغة العربية", "description": "ميدان في اللغة العربية", "parent_name": "اللغة العربية"},
                
                # ميادين الرياضيات
                {"name": "الاعداد و الحساب", "description": "ميدان في الرياضيات", "parent_name": "الرياضيات"},
                {"name": "الفضاء و الهندسة", "description": "ميدان في الرياضيات", "parent_name": "الرياضيات"},
                {"name": "المقادير و القياس", "description": "ميدان في الرياضيات", "parent_name": "الرياضيات"},
                {"name": "تنظيم المعطيات", "description": "ميدان في الرياضيات", "parent_name": "الرياضيات"},
                {"name": "تطبيقات الرياضيات", "description": "ميدان في الرياضيات", "parent_name": "الرياضيات"},
                {"name": "معالجة الرياضيات", "description": "ميدان في الرياضيات", "parent_name": "الرياضيات"},
                {"name": "تقويم فصلي", "description": "ميدان في الرياضيات", "parent_name": "الرياضيات"},
                
                # ميادين التربية الإسلامية
                {"name": "القرآن والحديث", "description": "ميدان في التربية الإسلامية", "parent_name": "التربية الاسلامية"},
                {"name": "تهذيب السلوك", "description": "ميدان في التربية الإسلامية", "parent_name": "التربية الاسلامية"},
                {"name": "مبادئ في العقيدة", "description": "ميدان في التربية الإسلامية", "parent_name": "التربية الاسلامية"},
                {"name": "مبادئ في السيرة", "description": "ميدان في التربية الإسلامية", "parent_name": "التربية الاسلامية"},
                {"name": "استعراض النص الشرعي", "description": "ميدان في التربية الإسلامية", "parent_name": "التربية الاسلامية"},
                {"name": "تقويم فصلي", "description": "ميدان في التربية الإسلامية", "parent_name": "التربية الاسلامية"},
                {"name": "حفظ سورة/آية", "description": "ميدان في التربية الإسلامية", "parent_name": "حفظ القرآن"},
                
                # ميادين الفرنسية
                {"name": "français_leçon", "description": "الفرنسية", "parent_name": "الفرنسية"},
                {"name": "تقويم فصلي", "description": "الفرنسية", "parent_name": "الفرنسية"},
                
                # ميادين التربية العلمية
                {"name": "الإنسان و الصحة", "description": "ميدان في التربية العلمية", "parent_name": "التربية العلمية"},
                {"name": "الإنسان و المحيط", "description": "ميدان في التربية العلمية", "parent_name": "التربية العلمية"},
                {"name": "المعلمة في الفضاء و الزمن", "description": "ميدان في التربية العلمية", "parent_name": "التربية العلمية"},
                {"name": "المادة و علم الأشياء", "description": "ميدان في التربية العلمية", "parent_name": "التربية العلمية"},
                {"name": "تقويم فصلي", "description": "ميدان في التربية العلمية", "parent_name": "التربية العلمية"},
                
                # ميادين التربية المدنية
                {"name": "الحياة الجماعية", "description": "ميدان في التربية المدنية", "parent_name": "التربية المدنية"},
                {"name": "الحياة المدنية", "description": "ميدان في التربية المدنية", "parent_name": "التربية المدنية"},
                {"name": "الحياة الديمقراطية و المؤسسات", "description": "ميدان في التربية المدنية", "parent_name": "التربية المدنية"},
                {"name": "تقويم فصلي", "description": "ميدان في التربية المدنية", "parent_name": "التربية المدنية"},
                
                # ميادين التربية الفنية والتشكيلية
                {"name": "الرسم و التلوين", "description": "ميدان في التربية الفنية و التشكيلية", "parent_name": "التربية الفنية /التشكيلية"},
                {"name": "فن التصميم", "description": "ميدان في التربية الفنية و التشكيلية", "parent_name": "التربية الفنية /التشكيلية"},
                {"name": "النشيد و الأغنية التربوية", "description": "ميدان في التربية الفنية و التشكيلية", "parent_name": "التربية الفنية /التشكيلية"},
                {"name": "التذوق الموسيقي و الاستماع", "description": "ميدان في التربية الفنية و التشكيلية", "parent_name": "التربية الفنية /التشكيلية"},
                {"name": "القواعد الموسيقية و النظريات", "description": "ميدان في التربية الفنية و التشكيلية", "parent_name": "التربية الفنية /التشكيلية"},
                {"name": "تقويم فصلي", "description": "ميدان في التربية الفنية و التشكيلية", "parent_name": "التربية الفنية /التشكيلية"},
                
                # ميادين التاريخ
                {"name": "أدوات و مفاهيم مادة التاريخ", "description": "ميدان في التاريخ", "parent_name": "التاريخ"},
                {"name": "التاريخ العام", "description": "ميدان في التاريخ", "parent_name": "التاريخ"},
                {"name": "التاريخ الوطني", "description": "ميدان في التاريخ", "parent_name": "التاريخ"},
                {"name": "تقويم فصلي", "description": "ميدان في التاريخ", "parent_name": "التاريخ"},
                
                # ميادين الجغرافيا
                {"name": "أدوات و مفاهيم مادة الجغرافيا", "description": "ميدان في الجغرافيا", "parent_name": "الجغرافيا"},
                {"name": "السكان و التنمية", "description": "ميدان في الجغرافيا", "parent_name": "الجغرافيا"},
                {"name": "السكان و البيئة", "description": "ميدان في الجغرافيا", "parent_name": "الجغرافيا"},
                {"name": "تقويم فصلي", "description": "ميدان في الجغرافيا", "parent_name": "الجغرافيا"},
                
                # ميادين التربية البدنية
                {"name": "الوضعيات و التنقلات", "description": "ميدان في التربية البدنية", "parent_name": "التربية البدنية"},
                {"name": "الحركات القاعدية", "description": "ميدان في التربية البدنية", "parent_name": "التربية البدنية"},
                {"name": "الهيكلة و البناء", "description": "ميدان في التربية البدنية", "parent_name": "التربية البدنية"},
                {"name": "تقويم فصلي", "description": "ميدان في التربية البدنية", "parent_name": "التربية البدنية"},
                
                # ميادين الأمازيغية
                {"name": "Tamazight", "description": "الأمازيغية", "parent_name": "الأمازيغية"},
                
                # ميادين الإنجليزية
                {"name": "English", "description": "الإنجليزية", "parent_name": "الإنجليزية"},
            ]
            
            # الحصول على جميع المستويات التعليمية
            levels = EducationalLevel.query.all()
            
            # إضافة المواد والميادين لكل مستوى تعليمي
            for level in levels:
                print(f"\n--- إعادة إنشاء قاعدة بيانات المستوى: {level.name} ---")
                
                # الحصول على قاعدة بيانات المستوى
                database = LevelDatabase.query.filter_by(level_id=level.id).first()
                
                if not database:
                    print(f"خطأ: لا توجد قاعدة بيانات للمستوى {level.name}")
                    continue
                
                # إضافة المواد الدراسية
                created_subjects = {}
                for subject_data in subjects:
                    subject = LevelDataEntry(
                        database_id=database.id,
                        entry_type='subject',
                        name=subject_data['name'],
                        description=subject_data['description'],
                        is_active=subject_data['is_active']
                    )
                    db.session.add(subject)
                    db.session.flush()  # للحصول على معرف العنصر
                    created_subjects[subject_data['name']] = subject.id
                
                print(f"تم إضافة {len(created_subjects)} مادة دراسية")
                
                # إضافة الميادين
                created_domains = {}
                for domain_data in domains_data:
                    if domain_data['parent_name'] in created_subjects:
                        domain = LevelDataEntry(
                            database_id=database.id,
                            entry_type='domain',
                            parent_id=created_subjects[domain_data['parent_name']],
                            name=domain_data['name'],
                            description=domain_data['description'],
                            is_active=True
                        )
                        db.session.add(domain)
                        db.session.flush()
                        created_domains[domain_data['name']] = domain.id
                
                print(f"تم إضافة {len(created_domains)} ميدان")
                
                db.session.commit()
                print(f"تم إعادة إنشاء قاعدة بيانات {database.name} بنجاح")
            
            print("\nتم الانتهاء من إعادة إنشاء جميع قواعد البيانات بنجاح")
            
        except Exception as e:
            db.session.rollback()
            print(f"حدث خطأ أثناء إعادة إنشاء قواعد البيانات: {str(e)}")

if __name__ == "__main__":
    reset_all_databases()
