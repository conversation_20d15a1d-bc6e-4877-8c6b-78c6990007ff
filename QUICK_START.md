# دليل التشغيل السريع لتطبيق Ta9affi

## الخطوات السريعة لتشغيل التطبيق

1. قم بتثبيت المتطلبات:
   ```
   pip install -r requirements.txt
   ```

2. قم بإضافة البيانات التجريبية:
   ```
   python seed_data.py
   ```

3. قم بتشغيل التطبيق:
   ```
   python run.py
   ```

4. افتح المتصفح وانتقل إلى:
   ```
   http://localhost:5000
   ```

## حسابات المستخدمين التجريبية

### حساب الإدارة
- اسم المستخدم: `admin`
- كلمة المرور: `admin123`

### حساب المفتش
- اسم المستخدم: `inspector`
- كلمة المرور: `inspector123`

### حساب الأستاذ
- اسم المستخدم: `teacher`
- كلمة المرور: `teacher123`

### حساب أستاذ آخر
- اسم المستخدم: `teacher2`
- كلمة المرور: `teacher123`

## ملاحظات هامة

- تم ربط المفتش بالأستاذين في النظام، بحيث يمكن للمفتش متابعة تقدمهم في البرنامج السنوي.
- يمكنك إنشاء حسابات جديدة من خلال صفحة التسجيل في التطبيق.
- البيانات التجريبية تشمل مستويات تعليمية ومواد وميادين وكفاءات مستهدفة للسنة الأولى ابتدائي كمثال.
