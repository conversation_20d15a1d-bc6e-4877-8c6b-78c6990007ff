from flask import render_template, request, redirect, url_for, flash, jsonify
from flask_login import login_user, login_required, logout_user, current_user
from werkzeug.security import generate_password_hash, check_password_hash
import pandas as pd
import os
from datetime import datetime

from app import app, db, login_manager
from models import User, Role, EducationalLevel, Subject, Domain, KnowledgeMaterial, Competency, Schedule, ProgressEntry, LevelDatabase, LevelDataEntry
import json

# User loader for Flask-Login
@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

# Home route
@app.route('/')
def index():
    return render_template('index.html')

# Authentication routes
@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')

        user = User.query.filter_by(username=username).first()

        if user and check_password_hash(user.password, password):
            login_user(user)
            flash('Login successful!', 'success')
            return redirect(url_for('dashboard'))
        else:
            flash('Invalid username or password', 'danger')

    return render_template('login.html')

@app.route('/register', methods=['GET', 'POST'])
def register():
    if request.method == 'POST':
        username = request.form.get('username')
        email = request.form.get('email')
        password = request.form.get('password')
        role = request.form.get('role')

        # Check if username or email already exists
        if User.query.filter_by(username=username).first():
            flash('Username already exists', 'danger')
            return render_template('register.html')

        if User.query.filter_by(email=email).first():
            flash('Email already exists', 'danger')
            return render_template('register.html')

        # Create new user
        hashed_password = generate_password_hash(password)
        new_user = User(username=username, email=email, password=hashed_password, role=role)

        db.session.add(new_user)
        db.session.commit()

        flash('Registration successful! Please login.', 'success')
        return redirect(url_for('login'))

    return render_template('register.html')

@app.route('/logout')
@login_required
def logout():
    logout_user()
    flash('You have been logged out', 'info')
    return redirect(url_for('index'))

# Dashboard routes
@app.route('/dashboard')
@login_required
def dashboard():
    if current_user.role == Role.ADMIN:
        return redirect(url_for('admin_dashboard'))
    elif current_user.role == Role.INSPECTOR:
        return redirect(url_for('inspector_dashboard'))
    else:
        return redirect(url_for('teacher_dashboard'))

@app.route('/dashboard/admin')
@login_required
def admin_dashboard():
    if current_user.role != Role.ADMIN:
        flash('Access denied', 'danger')
        return redirect(url_for('dashboard'))

    inspectors = User.query.filter_by(role=Role.INSPECTOR).all()
    return render_template('admin_dashboard.html', inspectors=inspectors)

@app.route('/dashboard/inspector')
@login_required
def inspector_dashboard():
    if current_user.role != Role.INSPECTOR:
        flash('Access denied', 'danger')
        return redirect(url_for('dashboard'))

    teachers = current_user.supervised_teachers.all()
    return render_template('inspector_dashboard.html', teachers=teachers)

@app.route('/dashboard/teacher')
@login_required
def teacher_dashboard():
    if current_user.role != Role.TEACHER:
        flash('Access denied', 'danger')
        return redirect(url_for('dashboard'))

    schedules = Schedule.query.filter_by(user_id=current_user.id).all()
    progress_entries = ProgressEntry.query.filter_by(user_id=current_user.id).order_by(ProgressEntry.date.desc()).limit(10).all()

    return render_template('teacher_dashboard.html', schedules=schedules, progress_entries=progress_entries)

# Teaching program routes
@app.route('/teaching-program')
@login_required
def teaching_program():
    levels = EducationalLevel.query.all()
    return render_template('teaching_program.html', levels=levels)

# API routes for dependent dropdowns
@app.route('/api/subjects/<int:level_id>')
def get_subjects(level_id):
    subjects = Subject.query.filter_by(level_id=level_id).all()
    return jsonify([{'id': s.id, 'name': s.name} for s in subjects])

@app.route('/api/domains/<int:subject_id>')
def get_domains(subject_id):
    domains = Domain.query.filter_by(subject_id=subject_id).all()
    return jsonify([{'id': d.id, 'name': d.name} for d in domains])

@app.route('/api/knowledge-materials/<int:domain_id>')
def get_knowledge_materials(domain_id):
    materials = KnowledgeMaterial.query.filter_by(domain_id=domain_id).all()
    return jsonify([{'id': m.id, 'name': m.name} for m in materials])

@app.route('/api/competencies/<int:material_id>')
def get_competencies(material_id):
    competencies = Competency.query.filter_by(knowledge_material_id=material_id).all()
    return jsonify([{'id': c.id, 'description': c.description} for c in competencies])

# Progress tracking routes
@app.route('/progress/add', methods=['POST'])
@login_required
def add_progress():
    competency_id = request.form.get('competency_id')
    date = datetime.strptime(request.form.get('date'), '%Y-%m-%d').date()
    status = request.form.get('status')
    notes = request.form.get('notes')

    new_progress = ProgressEntry(
        user_id=current_user.id,
        competency_id=competency_id,
        date=date,
        status=status,
        notes=notes
    )

    db.session.add(new_progress)
    db.session.commit()

    flash('Progress entry added successfully', 'success')
    return redirect(url_for('teaching_program'))

# Schedule management routes
@app.route('/schedule/manage')
@login_required
def manage_schedule():
    if current_user.role != Role.TEACHER:
        flash('Access denied', 'danger')
        return redirect(url_for('dashboard'))

    schedules = Schedule.query.filter_by(user_id=current_user.id).all()
    levels = EducationalLevel.query.all()

    return render_template('manage_schedule.html', schedules=schedules, levels=levels)

@app.route('/schedule/add', methods=['POST'])
@login_required
def add_schedule():
    day_of_week = int(request.form.get('day_of_week'))
    start_time = datetime.strptime(request.form.get('start_time'), '%H:%M').time()
    end_time = datetime.strptime(request.form.get('end_time'), '%H:%M').time()
    subject_id = request.form.get('subject_id')
    level_id = request.form.get('level_id')

    new_schedule = Schedule(
        user_id=current_user.id,
        day_of_week=day_of_week,
        start_time=start_time,
        end_time=end_time,
        subject_id=subject_id,
        level_id=level_id
    )

    db.session.add(new_schedule)
    db.session.commit()

    flash('Schedule added successfully', 'success')
    return redirect(url_for('manage_schedule'))

# Level Database Management Routes
@app.route('/admin/databases')
@login_required
def manage_databases():
    if current_user.role != Role.ADMIN:
        flash('Access denied', 'danger')
        return redirect(url_for('dashboard'))

    levels = EducationalLevel.query.all()
    databases = LevelDatabase.query.all()

    return render_template('manage_level_databases.html', levels=levels, databases=databases)

@app.route('/admin/databases/add', methods=['POST'])
@login_required
def add_database():
    if current_user.role != Role.ADMIN:
        flash('Access denied', 'danger')
        return redirect(url_for('dashboard'))

    level_id = request.form.get('level_id')
    name = request.form.get('name')
    file_path = request.form.get('file_path')
    is_active = 'is_active' in request.form

    # Check if level exists
    level = EducationalLevel.query.get(level_id)
    if not level:
        flash('المستوى التعليمي غير موجود', 'danger')
        return redirect(url_for('manage_databases'))

    # Create database directory if it doesn't exist
    os.makedirs(os.path.dirname(os.path.join(app.root_path, file_path)), exist_ok=True)

    # Create new database
    new_db = LevelDatabase(
        level_id=level_id,
        name=name,
        file_path=file_path,
        is_active=is_active
    )

    db.session.add(new_db)
    db.session.commit()

    flash('تم إضافة قاعدة البيانات بنجاح', 'success')
    return redirect(url_for('manage_databases'))

@app.route('/admin/databases/<int:db_id>/view')
@login_required
def view_database(db_id):
    if current_user.role != Role.ADMIN:
        flash('Access denied', 'danger')
        return redirect(url_for('dashboard'))

    database = LevelDatabase.query.get_or_404(db_id)
    entries = LevelDataEntry.query.filter_by(database_id=db_id).all()

    return render_template('view_database.html', database=database, entries=entries)

@app.route('/admin/databases/<int:db_id>/edit', methods=['GET', 'POST'])
@login_required
def edit_database(db_id):
    if current_user.role != Role.ADMIN:
        flash('Access denied', 'danger')
        return redirect(url_for('dashboard'))

    database = LevelDatabase.query.get_or_404(db_id)

    if request.method == 'POST':
        database.name = request.form.get('name')
        database.file_path = request.form.get('file_path')
        database.is_active = 'is_active' in request.form

        db.session.commit()

        flash('تم تحديث قاعدة البيانات بنجاح', 'success')
        return redirect(url_for('manage_databases'))

    return render_template('edit_database.html', database=database)

@app.route('/admin/databases/<int:db_id>/delete', methods=['POST'])
@login_required
def delete_database(db_id):
    if current_user.role != Role.ADMIN:
        flash('Access denied', 'danger')
        return redirect(url_for('dashboard'))

    database = LevelDatabase.query.get_or_404(db_id)

    # Delete all entries first
    LevelDataEntry.query.filter_by(database_id=db_id).delete()

    # Delete database
    db.session.delete(database)
    db.session.commit()

    flash('تم حذف قاعدة البيانات بنجاح', 'success')
    return redirect(url_for('manage_databases'))

@app.route('/admin/databases/<int:db_id>/toggle/<string:action>')
@login_required
def toggle_database(db_id, action):
    if current_user.role != Role.ADMIN:
        flash('Access denied', 'danger')
        return redirect(url_for('dashboard'))

    database = LevelDatabase.query.get_or_404(db_id)

    if action == 'activate':
        database.is_active = True
        flash('تم تفعيل قاعدة البيانات بنجاح', 'success')
    elif action == 'deactivate':
        database.is_active = False
        flash('تم تعطيل قاعدة البيانات بنجاح', 'success')

    db.session.commit()
    return redirect(url_for('manage_databases'))

# Database Entries Management
@app.route('/admin/databases/<int:db_id>/entries/add', methods=['POST'])
@login_required
def add_database_entry(db_id):
    if current_user.role != Role.ADMIN:
        flash('Access denied', 'danger')
        return redirect(url_for('dashboard'))

    database = LevelDatabase.query.get_or_404(db_id)

    entry_type = request.form.get('entry_type')
    parent_id = request.form.get('parent_id')
    name = request.form.get('name')
    description = request.form.get('description')
    is_active = 'is_active' in request.form

    # Create new entry
    new_entry = LevelDataEntry(
        database_id=db_id,
        entry_type=entry_type,
        parent_id=parent_id if parent_id != '0' else None,
        name=name,
        description=description,
        is_active=is_active
    )

    db.session.add(new_entry)
    db.session.commit()

    flash('تم إضافة العنصر بنجاح', 'success')
    return redirect(url_for('view_database', db_id=db_id))

@app.route('/admin/databases/<int:db_id>/entries/<int:entry_id>/edit', methods=['POST'])
@login_required
def edit_database_entry(db_id, entry_id):
    if current_user.role != Role.ADMIN:
        flash('Access denied', 'danger')
        return redirect(url_for('dashboard'))

    entry = LevelDataEntry.query.get_or_404(entry_id)

    entry.name = request.form.get('name')
    entry.description = request.form.get('description')
    entry.is_active = 'is_active' in request.form

    db.session.commit()

    flash('تم تحديث العنصر بنجاح', 'success')
    return redirect(url_for('view_database', db_id=db_id))

@app.route('/admin/databases/<int:db_id>/entries/<int:entry_id>/delete', methods=['POST'])
@login_required
def delete_database_entry(db_id, entry_id):
    if current_user.role != Role.ADMIN:
        flash('Access denied', 'danger')
        return redirect(url_for('dashboard'))

    entry = LevelDataEntry.query.get_or_404(entry_id)

    # Delete all child entries recursively
    delete_child_entries(db_id, entry_id)

    # Delete entry
    db.session.delete(entry)
    db.session.commit()

    flash('تم حذف العنصر بنجاح', 'success')
    return redirect(url_for('view_database', db_id=db_id))

# Helper function to delete child entries recursively
def delete_child_entries(db_id, parent_id):
    child_entries = LevelDataEntry.query.filter_by(database_id=db_id, parent_id=parent_id).all()

    for entry in child_entries:
        # Delete children of this entry
        delete_child_entries(db_id, entry.id)

        # Delete this entry
        db.session.delete(entry)

@app.route('/admin/databases/<int:db_id>/import', methods=['POST'])
@login_required
def import_database_data(db_id):
    if current_user.role != Role.ADMIN:
        flash('Access denied', 'danger')
        return redirect(url_for('dashboard'))

    database = LevelDatabase.query.get_or_404(db_id)

    if 'file' not in request.files:
        flash('لم يتم تحديد ملف', 'danger')
        return redirect(url_for('manage_databases'))

    file = request.files['file']

    if file.filename == '':
        flash('لم يتم تحديد ملف', 'danger')
        return redirect(url_for('manage_databases'))

    if not file.filename.endswith('.xlsx'):
        flash('يجب أن يكون الملف بصيغة Excel (.xlsx)', 'danger')
        return redirect(url_for('manage_databases'))

    try:
        # Read Excel file
        df = pd.read_excel(file, sheet_name=None)  # Read all sheets

        # Process each sheet
        for sheet_name, sheet_data in df.items():
            if sheet_name.lower() in ['subjects', 'domains', 'materials', 'competencies']:
                entry_type = sheet_name.lower().rstrip('s')  # Remove trailing 's'

                for _, row in sheet_data.iterrows():
                    # Convert row to dict
                    row_dict = row.to_dict()

                    # Get parent name if applicable
                    parent_id = None
                    if 'parent_name' in row_dict and entry_type != 'subject':
                        parent_name = row_dict['parent_name']
                        parent_type = {
                            'domain': 'subject',
                            'material': 'domain',
                            'competency': 'material'
                        }[entry_type]

                        parent = LevelDataEntry.query.filter_by(
                            database_id=db_id,
                            entry_type=parent_type,
                            name=parent_name
                        ).first()

                        if parent:
                            parent_id = parent.id

                    # Create new entry
                    new_entry = LevelDataEntry(
                        database_id=db_id,
                        entry_type=entry_type,
                        parent_id=parent_id,
                        name=row_dict.get('name', ''),
                        description=row_dict.get('description', ''),
                        is_active=True
                    )

                    db.session.add(new_entry)

        db.session.commit()
        flash('تم استيراد البيانات بنجاح', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء استيراد البيانات: {str(e)}', 'danger')

    return redirect(url_for('view_database', db_id=db_id))

@app.route('/admin/databases/<int:db_id>/export')
@login_required
def export_database_data(db_id):
    if current_user.role != Role.ADMIN:
        flash('Access denied', 'danger')
        return redirect(url_for('dashboard'))

    database = LevelDatabase.query.get_or_404(db_id)

    # Create a writer for Excel file
    filename = f"database_{database.name}_{datetime.now().strftime('%Y%m%d%H%M%S')}.xlsx"
    filepath = os.path.join(app.root_path, 'static', 'exports', filename)
    os.makedirs(os.path.dirname(filepath), exist_ok=True)

    writer = pd.ExcelWriter(filepath, engine='openpyxl')

    # Export subjects
    subjects = LevelDataEntry.query.filter_by(database_id=db_id, entry_type='subject').all()
    subjects_data = [{
        'id': s.id,
        'name': s.name,
        'description': s.description,
        'is_active': s.is_active
    } for s in subjects]

    if subjects_data:
        pd.DataFrame(subjects_data).to_excel(writer, sheet_name='Subjects', index=False)

    # Export domains
    domains = LevelDataEntry.query.filter_by(database_id=db_id, entry_type='domain').all()
    domains_data = []

    for d in domains:
        parent = LevelDataEntry.query.get(d.parent_id) if d.parent_id else None
        domains_data.append({
            'id': d.id,
            'name': d.name,
            'description': d.description,
            'parent_id': d.parent_id,
            'parent_name': parent.name if parent else '',
            'is_active': d.is_active
        })

    if domains_data:
        pd.DataFrame(domains_data).to_excel(writer, sheet_name='Domains', index=False)

    # Export materials
    materials = LevelDataEntry.query.filter_by(database_id=db_id, entry_type='material').all()
    materials_data = []

    for m in materials:
        parent = LevelDataEntry.query.get(m.parent_id) if m.parent_id else None
        materials_data.append({
            'id': m.id,
            'name': m.name,
            'description': m.description,
            'parent_id': m.parent_id,
            'parent_name': parent.name if parent else '',
            'is_active': m.is_active
        })

    if materials_data:
        pd.DataFrame(materials_data).to_excel(writer, sheet_name='Materials', index=False)

    # Export competencies
    competencies = LevelDataEntry.query.filter_by(database_id=db_id, entry_type='competency').all()
    competencies_data = []

    for c in competencies:
        parent = LevelDataEntry.query.get(c.parent_id) if c.parent_id else None
        competencies_data.append({
            'id': c.id,
            'name': c.name,
            'description': c.description,
            'parent_id': c.parent_id,
            'parent_name': parent.name if parent else '',
            'is_active': c.is_active
        })

    if competencies_data:
        pd.DataFrame(competencies_data).to_excel(writer, sheet_name='Competencies', index=False)

    writer.close()

    return redirect(url_for('static', filename=f'exports/{filename}'))

# Data import/export routes (Legacy)
@app.route('/data/export/<string:model_name>')
@login_required
def export_data(model_name):
    if current_user.role != Role.ADMIN:
        flash('Access denied', 'danger')
        return redirect(url_for('dashboard'))

    model_map = {
        'levels': EducationalLevel,
        'subjects': Subject,
        'domains': Domain,
        'materials': KnowledgeMaterial,
        'competencies': Competency
    }

    if model_name not in model_map:
        flash('Invalid model name', 'danger')
        return redirect(url_for('admin_dashboard'))

    model = model_map[model_name]
    data = model.query.all()

    # Convert to DataFrame
    df_data = []
    for item in data:
        item_dict = {column.name: getattr(item, column.name) for column in item.__table__.columns}
        df_data.append(item_dict)

    df = pd.DataFrame(df_data)

    # Save to Excel
    filename = f"{model_name}_{datetime.now().strftime('%Y%m%d%H%M%S')}.xlsx"
    filepath = os.path.join(app.root_path, 'static', 'exports', filename)
    os.makedirs(os.path.dirname(filepath), exist_ok=True)
    df.to_excel(filepath, index=False)

    return redirect(url_for('static', filename=f'exports/{filename}'))

@app.route('/data/import/<string:model_name>', methods=['POST'])
@login_required
def import_data(model_name):
    if current_user.role != Role.ADMIN:
        flash('Access denied', 'danger')
        return redirect(url_for('dashboard'))

    model_map = {
        'levels': EducationalLevel,
        'subjects': Subject,
        'domains': Domain,
        'materials': KnowledgeMaterial,
        'competencies': Competency
    }

    if model_name not in model_map:
        flash('Invalid model name', 'danger')
        return redirect(url_for('admin_dashboard'))

    if 'file' not in request.files:
        flash('No file part', 'danger')
        return redirect(url_for('admin_dashboard'))

    file = request.files['file']

    if file.filename == '':
        flash('No selected file', 'danger')
        return redirect(url_for('admin_dashboard'))

    if not file.filename.endswith('.xlsx'):
        flash('File must be an Excel file (.xlsx)', 'danger')
        return redirect(url_for('admin_dashboard'))

    # Read Excel file
    df = pd.read_excel(file)

    # Import data
    model = model_map[model_name]
    for _, row in df.iterrows():
        # Convert row to dict
        row_dict = row.to_dict()

        # Remove id column if present
        if 'id' in row_dict:
            del row_dict['id']

        # Create new instance
        new_instance = model(**row_dict)
        db.session.add(new_instance)

    db.session.commit()

    flash(f'Data imported successfully for {model_name}', 'success')
    return redirect(url_for('admin_dashboard'))
