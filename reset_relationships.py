from app_new import app, db, User, Role, inspector_teacher

def reset_relationships():
    with app.app_context():
        # البحث عن مفتش الاختبار
        inspector = User.query.filter_by(username='inspector_test').first()
        
        if not inspector:
            print("مفتش الاختبار غير موجود")
            return
        
        print(f"إعادة تعيين العلاقات للمفتش: {inspector.username} (ID: {inspector.id})")
        
        # حذف جميع العلاقات الحالية
        db.session.execute(inspector_teacher.delete().where(inspector_teacher.c.inspector_id == inspector.id))
        db.session.commit()
        print("تم حذف جميع العلاقات الحالية")
        
        # الحصول على جميع الأساتذة
        teachers = User.query.filter_by(role=Role.TEACHER).all()
        print(f"عدد الأساتذة المتاحين: {len(teachers)}")
        
        # إنشاء علاقات جديدة
        for teacher in teachers:
            stmt = inspector_teacher.insert().values(
                inspector_id=inspector.id,
                teacher_id=teacher.id
            )
            db.session.execute(stmt)
            print(f"تم إضافة العلاقة مع الأستاذ: {teacher.username} (ID: {teacher.id})")
        
        db.session.commit()
        print("تم إنشاء العلاقات الجديدة بنجاح")
        
        # التحقق من العلاقات الجديدة
        relationships = db.session.query(inspector_teacher).filter(
            inspector_teacher.c.inspector_id == inspector.id
        ).all()
        
        print(f"\nعدد العلاقات الجديدة: {len(relationships)}")
        
        for i, rel in enumerate(relationships, 1):
            teacher = User.query.get(rel.teacher_id)
            teacher_name = teacher.username if teacher else "غير موجود"
            print(f"{i}. المفتش: {inspector.id} - الأستاذ: {rel.teacher_id} ({teacher_name})")

if __name__ == "__main__":
    reset_relationships()
