{% extends 'base.html' %}

{% block title %}الإشعارات - Ta9affi{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-bell me-2"></i>
                        الإشعارات بين الإدارة والمفتشين
                    </h4>
                </div>
                <div class="card-body">
                    {% if current_user.is_admin() %}
                        <!-- واجهة المدير -->
                        <div class="row">
                            <div class="col-md-4">
                                <div class="card border-0 bg-light">
                                    <div class="card-header bg-success text-white">
                                        <h5 class="mb-0">
                                            <i class="fas fa-paper-plane me-1"></i>
                                            إرسال إشعار جديد
                                        </h5>
                                    </div>
                                    <div class="card-body">
                                        <form action="{{ url_for('admin.send_notification') }}" method="post">
                                            <div class="mb-3">
                                                <label for="receiver_id" class="form-label">المستلم</label>
                                                <select class="form-select" id="receiver_id" name="receiver_id" required>
                                                    <option value="">اختر المفتش</option>
                                                    <!-- سيتم ملء هذه القائمة من قاعدة البيانات -->
                                                    <option value="1">مفتش تجريبي</option>
                                                </select>
                                            </div>
                                            <div class="mb-3">
                                                <label for="title" class="form-label">العنوان</label>
                                                <input type="text" class="form-control" id="title" name="title" required>
                                            </div>
                                            <div class="mb-3">
                                                <label for="message" class="form-label">الرسالة</label>
                                                <textarea class="form-control" id="message" name="message" rows="4" required></textarea>
                                            </div>
                                            <div class="d-grid">
                                                <button type="submit" class="btn btn-success">
                                                    <i class="fas fa-send me-1"></i>
                                                    إرسال الإشعار
                                                </button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-8">
                                <div class="card border-0 bg-light">
                                    <div class="card-header bg-info text-white">
                                        <h5 class="mb-0">
                                            <i class="fas fa-history me-1"></i>
                                            الإشعارات المرسلة
                                        </h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="table-responsive">
                                            <table class="table table-striped table-hover">
                                                <thead class="table-light">
                                                    <tr>
                                                        <th>المستلم</th>
                                                        <th>العنوان</th>
                                                        <th>التاريخ</th>
                                                        <th>الحالة</th>
                                                        <th>الإجراءات</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <!-- سيتم ملء هذه البيانات من قاعدة البيانات -->
                                                    <tr>
                                                        <td colspan="5" class="text-center text-muted">
                                                            <i class="fas fa-info-circle me-1"></i>
                                                            لا توجد إشعارات مرسلة حتى الآن
                                                        </td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    {% elif current_user.is_inspector() %}
                        <!-- واجهة المفتش -->
                        <div class="row">
                            <div class="col-md-12">
                                <div class="card border-0 bg-light">
                                    <div class="card-header bg-warning text-dark">
                                        <h5 class="mb-0">
                                            <i class="fas fa-inbox me-1"></i>
                                            الإشعارات الواردة من الإدارة
                                        </h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="table-responsive">
                                            <table class="table table-striped table-hover">
                                                <thead class="table-light">
                                                    <tr>
                                                        <th>المرسل</th>
                                                        <th>العنوان</th>
                                                        <th>التاريخ</th>
                                                        <th>الحالة</th>
                                                        <th>الإجراءات</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <!-- سيتم ملء هذه البيانات من قاعدة البيانات -->
                                                    <tr>
                                                        <td colspan="5" class="text-center text-muted">
                                                            <i class="fas fa-info-circle me-1"></i>
                                                            لا توجد إشعارات واردة حتى الآن
                                                        </td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal لعرض تفاصيل الإشعار -->
<div class="modal fade" id="notificationModal" tabindex="-1" aria-labelledby="notificationModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="notificationModalLabel">تفاصيل الإشعار</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div id="notificationContent">
                    <!-- سيتم ملء المحتوى بواسطة JavaScript -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                <button type="button" class="btn btn-primary" id="markAsRead">تحديد كمقروء</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // التعامل مع عرض تفاصيل الإشعار
        const notificationModal = document.getElementById('notificationModal');
        const notificationContent = document.getElementById('notificationContent');
        const markAsReadBtn = document.getElementById('markAsRead');

        // عند النقر على زر عرض الإشعار
        document.querySelectorAll('.view-notification').forEach(button => {
            button.addEventListener('click', function() {
                const notificationId = this.getAttribute('data-id');
                const title = this.getAttribute('data-title');
                const message = this.getAttribute('data-message');
                const sender = this.getAttribute('data-sender');
                const date = this.getAttribute('data-date');

                // ملء محتوى المودال
                notificationContent.innerHTML = `
                    <div class="mb-3">
                        <strong>العنوان:</strong> ${title}
                    </div>
                    <div class="mb-3">
                        <strong>المرسل:</strong> ${sender}
                    </div>
                    <div class="mb-3">
                        <strong>التاريخ:</strong> ${date}
                    </div>
                    <div class="mb-3">
                        <strong>الرسالة:</strong>
                        <div class="border p-3 mt-2 bg-light">
                            ${message}
                        </div>
                    </div>
                `;

                // تحديث زر "تحديد كمقروء"
                markAsReadBtn.setAttribute('data-id', notificationId);

                // عرض المودال
                const modal = new bootstrap.Modal(notificationModal);
                modal.show();
            });
        });

        // التعامل مع تحديد الإشعار كمقروء
        markAsReadBtn.addEventListener('click', function() {
            const notificationId = this.getAttribute('data-id');
            
            // إرسال طلب AJAX لتحديد الإشعار كمقروء
            fetch(`/admin/notifications/${notificationId}/mark-read`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // إعادة تحميل الصفحة لتحديث الحالة
                    location.reload();
                } else {
                    alert('حدث خطأ أثناء تحديث حالة الإشعار');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('حدث خطأ أثناء تحديث حالة الإشعار');
            });
        });

        console.log('تم تحميل صفحة الإشعارات بين الإدارة والمفتشين');
    });
</script>
{% endblock %}
