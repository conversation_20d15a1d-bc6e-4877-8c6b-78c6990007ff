from app_new import app, db, LevelDataEntry

def main():
    with app.app_context():
        # الحصول على جميع الميادين في السنة الرابعة ابتدائي
        domains = LevelDataEntry.query.filter_by(database_id=4, entry_type='domain').all()
        
        print(f'إجمالي عدد الميادين في السنة الرابعة ابتدائي: {len(domains)}')
        
        # الحصول على جميع المواد في السنة الرابعة ابتدائي
        subjects = LevelDataEntry.query.filter_by(database_id=4, entry_type='subject').all()
        
        # عرض عدد الميادين لكل مادة
        for subject in subjects:
            subject_domains = LevelDataEntry.query.filter_by(database_id=4, entry_type='domain', parent_id=subject.id).all()
            print(f'- {subject.name}: {len(subject_domains)} ميدان')

if __name__ == "__main__":
    main()
