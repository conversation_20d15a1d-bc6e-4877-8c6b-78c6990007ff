{% extends 'base.html' %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>
                    <i class="fas fa-chart-line me-2"></i>
                    تقدم الأستاذ: {{ teacher.username }}
                </h2>
                <div>
                    <a href="{{ url_for('inspector.manage_teachers') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-1"></i> العودة
                    </a>
                    <a href="{{ url_for('inspector.send_notification', teacher_id=teacher.id) }}" class="btn btn-primary">
                        <i class="fas fa-bell me-1"></i> إرسال إشعار
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- معلومات الأستاذ -->
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-user me-1"></i>
                        معلومات الأستاذ
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <strong>اسم المستخدم:</strong> {{ teacher.username }}
                        </div>
                        <div class="col-md-3">
                            <strong>البريد الإلكتروني:</strong> {{ teacher.email }}
                        </div>
                        <div class="col-md-3">
                            <strong>الدور:</strong> {{ teacher.role }}
                        </div>
                        <div class="col-md-3">
                            <strong>تاريخ التسجيل:</strong> {{ teacher.created_at.strftime('%Y-%m-%d') }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- إحصائيات التقدم -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card text-white bg-success">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>{{ completed_count }}</h4>
                            <p class="mb-0">مكتمل</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-check-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-white bg-warning">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>{{ in_progress_count }}</h4>
                            <p class="mb-0">قيد التنفيذ</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-clock fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-white bg-info">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>{{ planned_count }}</h4>
                            <p class="mb-0">مخطط</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-calendar fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-white bg-primary">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>{{ total_count }}</h4>
                            <p class="mb-0">الإجمالي</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-list fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- نسبة الإكمال -->
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-pie me-1"></i>
                        نسبة الإكمال الإجمالية
                    </h5>
                </div>
                <div class="card-body">
                    <div class="progress mb-2" style="height: 30px;">
                        <div class="progress-bar bg-success" role="progressbar" 
                             style="width: {{ completion_rate|round|int }}%;" 
                             aria-valuenow="{{ completion_rate|round|int }}" 
                             aria-valuemin="0" aria-valuemax="100">
                            {{ completion_rate|round|int }}%
                        </div>
                    </div>
                    <div class="text-center">
                        <strong>{{ completion_rate|round(1) }}% من البرنامج السنوي مكتمل</strong>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- جدول التقدم -->
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-table me-1"></i>
                        تفاصيل التقدم
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>التاريخ</th>
                                    <th>المستوى</th>
                                    <th>المادة</th>
                                    <th>الميدان</th>
                                    <th>المادة المعرفية</th>
                                    <th>الكفاءة المستهدفة</th>
                                    <th>الحالة</th>
                                    <th>الملاحظات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% if progress_entries %}
                                    {% for entry in progress_entries %}
                                    <tr>
                                        <td>{{ entry.date.strftime('%Y-%m-%d') if entry.date else 'غير محدد' }}</td>
                                        <td>{{ entry.level.name if entry.level else 'غير محدد' }}</td>
                                        <td>{{ entry.subject.name if entry.subject else 'غير محدد' }}</td>
                                        <td>{{ entry.domain.name if entry.domain else 'غير محدد' }}</td>
                                        <td>{{ entry.material.name if entry.material else 'غير محدد' }}</td>
                                        <td>
                                            <span class="competency-text" title="{{ entry.competency.description if entry.competency else 'غير محدد' }}">
                                                {{ entry.competency.description[:50] + '...' if entry.competency and entry.competency.description|length > 50 else (entry.competency.description if entry.competency else 'غير محدد') }}
                                            </span>
                                        </td>
                                        <td>
                                            {% if entry.status == 'completed' %}
                                            <span class="badge bg-success">مكتمل</span>
                                            {% elif entry.status == 'in_progress' %}
                                            <span class="badge bg-warning">قيد التنفيذ</span>
                                            {% else %}
                                            <span class="badge bg-info">مخطط</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <span title="{{ entry.notes or 'لا توجد ملاحظات' }}">
                                                {{ entry.notes[:30] + '...' if entry.notes and entry.notes|length > 30 else (entry.notes or 'لا توجد ملاحظات') }}
                                            </span>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                {% else %}
                                    <tr>
                                        <td colspan="8" class="text-center">لا توجد سجلات تقدم لهذا الأستاذ حتى الآن</td>
                                    </tr>
                                {% endif %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .competency-text {
        max-width: 200px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        cursor: help;
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        console.log('تم تحميل صفحة عرض تقدم الأستاذ');
        
        // إضافة tooltips للنصوص المقطوعة
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[title]'));
        const tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    });
</script>
{% endblock %}
