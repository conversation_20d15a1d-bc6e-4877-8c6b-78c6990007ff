"""مسارات الأستاذ لتطبيق Ta9affi"""

from flask import Blueprint, render_template, redirect, url_for, flash, request, jsonify
from flask_login import login_required, current_user
from app.models import User, Role, EducationalLevel, Subject, Domain, KnowledgeMaterial, Competency, Schedule, ProgressEntry, InspectorTeacherNotification
from app import db
from app.utils.decorators import teacher_required
from datetime import datetime, date, time

# إنشاء بلوبرنت للأستاذ
teacher_bp = Blueprint('teacher', __name__)

@teacher_bp.route('/dashboard')
@login_required
@teacher_required
def dashboard():
    """
    لوحة تحكم الأستاذ
    """
    # جلب بيانات تقدم الأستاذ
    progress_entries = current_user.progress_entries

    # حساب إحصائيات التقدم
    completed_count = sum(1 for entry in progress_entries if entry.status == 'completed')
    in_progress_count = sum(1 for entry in progress_entries if entry.status == 'in_progress')
    planned_count = sum(1 for entry in progress_entries if entry.status == 'planned')
    total_count = len(progress_entries)

    # حساب نسبة الإكمال
    completion_rate = (completed_count / total_count * 100) if total_count > 0 else 0

    # إعداد إحصائيات التقدم
    progress_stats = {
        'completed': completed_count,
        'in_progress': in_progress_count,
        'planned': planned_count,
        'total': total_count
    }

    # حساب إحصائيات المستويات
    level_stats = {}
    levels = EducationalLevel.query.all()
    for level in levels:
        level_entries = [entry for entry in progress_entries if entry.level_id == level.id]
        if level_entries:
            level_completed = sum(1 for entry in level_entries if entry.status == 'completed')
            level_in_progress = sum(1 for entry in level_entries if entry.status == 'in_progress')
            level_planned = sum(1 for entry in level_entries if entry.status == 'planned')
            level_total = len(level_entries)
            level_completion_rate = (level_completed / level_total * 100) if level_total > 0 else 0

            level_stats[level.id] = {
                'name': level.name,
                'completed': level_completed,
                'in_progress': level_in_progress,
                'planned': level_planned,
                'total': level_total,
                'completion_rate': level_completion_rate
            }

    # حساب إحصائيات المواد
    subject_stats = {}
    subjects = Subject.query.all()
    for subject in subjects:
        subject_entries = [entry for entry in progress_entries if entry.subject_id == subject.id]
        if subject_entries:
            subject_completed = sum(1 for entry in subject_entries if entry.status == 'completed')
            subject_in_progress = sum(1 for entry in subject_entries if entry.status == 'in_progress')
            subject_planned = sum(1 for entry in subject_entries if entry.status == 'planned')
            subject_total = len(subject_entries)
            subject_completion_rate = (subject_completed / subject_total * 100) if subject_total > 0 else 0

            subject_stats[subject.id] = {
                'name': subject.name,
                'completed': subject_completed,
                'in_progress': subject_in_progress,
                'planned': subject_planned,
                'total': subject_total,
                'completion_rate': subject_completion_rate
            }

    # جلب جدول الأستاذ الأسبوعي
    schedules = current_user.schedules

    # تنظيم الجدول حسب أيام الأسبوع
    weekly_schedule = {day: [] for day in range(7)}  # 0=الاثنين، 6=الأحد
    for schedule in schedules:
        weekly_schedule[schedule.day_of_week].append(schedule)

    return render_template('teacher_dashboard.html',
                           progress_entries=progress_entries,
                           completed_count=completed_count,
                           in_progress_count=in_progress_count,
                           planned_count=planned_count,
                           total_count=total_count,
                           completion_rate=completion_rate,
                           progress_stats=progress_stats,
                           level_stats=level_stats,
                           subject_stats=subject_stats,
                           weekly_schedule=weekly_schedule)

@teacher_bp.route('/view_progress')
@login_required
@teacher_required
def view_progress():
    """
    عرض تقدم الأستاذ في البرنامج السنوي
    """
    # جلب بيانات تقدم الأستاذ
    progress_entries = current_user.progress_entries

    # تنظيم البيانات حسب المستوى والمادة والميدان
    organized_progress = {}
    for entry in progress_entries:
        level_id = entry.level_id
        subject_id = entry.subject_id
        domain_id = entry.domain_id

        if level_id not in organized_progress:
            organized_progress[level_id] = {}

        if subject_id not in organized_progress[level_id]:
            organized_progress[level_id][subject_id] = {}

        if domain_id not in organized_progress[level_id][subject_id]:
            organized_progress[level_id][subject_id][domain_id] = []

        organized_progress[level_id][subject_id][domain_id].append(entry)

    return render_template('teacher/view_progress.html',
                           progress=organized_progress)

@teacher_bp.route('/add_progress', methods=['GET', 'POST'])
@login_required
@teacher_required
def add_progress():
    """
    إضافة تقدم جديد
    """
    # جلب المستويات التعليمية النشطة
    levels = EducationalLevel.query.filter_by(is_active=True).all()

    if request.method == 'POST':
        level_id = request.form.get('level_id')
        subject_id = request.form.get('subject_id')
        domain_id = request.form.get('domain_id')
        material_id = request.form.get('material_id')
        competency_id = request.form.get('competency_id')
        status = request.form.get('status')
        notes = request.form.get('notes')

        # التحقق من وجود جميع البيانات المطلوبة
        if not all([level_id, subject_id, domain_id, material_id, competency_id, status]):
            flash('يرجى ملء جميع الحقول المطلوبة.', 'danger')
            return redirect(url_for('teacher.add_progress'))

        # التحقق من عدم وجود تسجيل سابق لنفس الكفاءة
        existing_entry = ProgressEntry.query.filter_by(
            user_id=current_user.id,
            competency_id=competency_id
        ).first()

        if existing_entry:
            # تحديث التسجيل الموجود
            existing_entry.status = status
            existing_entry.notes = notes
            existing_entry.updated_at = datetime.utcnow()
            db.session.commit()
            flash('تم تحديث تقدم الكفاءة بنجاح!', 'success')
        else:
            # إنشاء تسجيل جديد
            new_entry = ProgressEntry(
                user_id=current_user.id,
                level_id=level_id,
                subject_id=subject_id,
                domain_id=domain_id,
                material_id=material_id,
                competency_id=competency_id,
                date=date.today(),
                status=status,
                notes=notes
            )
            db.session.add(new_entry)
            db.session.commit()
            flash('تم إضافة تقدم الكفاءة بنجاح!', 'success')

        return redirect(url_for('teacher.view_progress'))

    return render_template('teacher/add_progress.html', levels=levels)

@teacher_bp.route('/manage_schedule')
@login_required
@teacher_required
def manage_schedule():
    """
    عرض جدول الأستاذ الأسبوعي
    """
    # جلب جدول الأستاذ
    schedules = current_user.schedules

    # تنظيم الجدول حسب أيام الأسبوع
    weekly_schedule = {day: [] for day in range(7)}  # 0=الاثنين، 6=الأحد
    for schedule in schedules:
        weekly_schedule[schedule.day_of_week].append(schedule)

    return render_template('manage_schedule.html', weekly_schedule=weekly_schedule)

@teacher_bp.route('/add_schedule', methods=['GET', 'POST'])
@login_required
@teacher_required
def add_schedule():
    """
    إضافة حصة جديدة إلى الجدول
    """
    # جلب المستويات التعليمية والمواد
    levels = EducationalLevel.query.filter_by(is_active=True).all()
    subjects = Subject.query.all()

    if request.method == 'POST':
        day_of_week = int(request.form.get('day_of_week'))
        start_hour = int(request.form.get('start_hour'))
        start_minute = int(request.form.get('start_minute'))
        end_hour = int(request.form.get('end_hour'))
        end_minute = int(request.form.get('end_minute'))
        subject_id = request.form.get('subject_id')
        level_id = request.form.get('level_id')

        # التحقق من صحة البيانات
        if not (0 <= day_of_week <= 6):
            flash('يوم الأسبوع غير صالح.', 'danger')
            return redirect(url_for('teacher.add_schedule'))

        # إنشاء كائنات الوقت
        start_time = time(hour=start_hour, minute=start_minute)
        end_time = time(hour=end_hour, minute=end_minute)

        # التحقق من أن وقت البداية قبل وقت النهاية
        if start_time >= end_time:
            flash('يجب أن يكون وقت البداية قبل وقت النهاية.', 'danger')
            return redirect(url_for('teacher.add_schedule'))

        # التحقق من عدم وجود تعارض في الجدول
        existing_schedules = Schedule.query.filter_by(
            user_id=current_user.id,
            day_of_week=day_of_week
        ).all()

        for schedule in existing_schedules:
            if (start_time < schedule.end_time and end_time > schedule.start_time):
                flash('هناك تعارض مع حصة أخرى في نفس الوقت.', 'danger')
                return redirect(url_for('teacher.add_schedule'))

        # إنشاء حصة جديدة
        new_schedule = Schedule(
            user_id=current_user.id,
            day_of_week=day_of_week,
            start_time=start_time,
            end_time=end_time,
            subject_id=subject_id,
            level_id=level_id
        )
        db.session.add(new_schedule)
        db.session.commit()

        flash('تم إضافة الحصة إلى الجدول بنجاح!', 'success')
        return redirect(url_for('teacher.manage_schedule'))

    return render_template('manage_schedule.html', levels=levels, subjects=subjects)

@teacher_bp.route('/delete_schedule/<int:schedule_id>', methods=['POST'])
@login_required
@teacher_required
def delete_schedule(schedule_id):
    """
    حذف حصة من الجدول
    """
    schedule = Schedule.query.get_or_404(schedule_id)

    # التحقق من أن الحصة تنتمي للأستاذ الحالي
    if schedule.user_id != current_user.id:
        flash('غير مصرح لك بحذف هذه الحصة.', 'danger')
        return redirect(url_for('teacher.manage_schedule'))

    db.session.delete(schedule)
    db.session.commit()

    flash('تم حذف الحصة من الجدول بنجاح!', 'success')
    return redirect(url_for('teacher.manage_schedule'))

@teacher_bp.route('/api/schedule/<int:schedule_id>')
@login_required
@teacher_required
def get_schedule(schedule_id):
    """
    الحصول على تفاصيل حصة معينة
    """
    from flask import jsonify

    schedule = Schedule.query.get_or_404(schedule_id)

    # التحقق من أن الحصة تنتمي للأستاذ الحالي
    if schedule.user_id != current_user.id:
        return jsonify({'error': 'غير مصرح بالوصول'}), 403

    # تحويل الوقت إلى تنسيق مناسب لحقل الإدخال من نوع time
    start_time = schedule.start_time.strftime('%H:%M')
    end_time = schedule.end_time.strftime('%H:%M')

    return jsonify({
        'id': schedule.id,
        'day_of_week': schedule.day_of_week,
        'start_time': start_time,
        'end_time': end_time,
        'subject_id': schedule.subject_id,
        'level_id': schedule.level_id
    })

@teacher_bp.route('/edit_schedule/<int:schedule_id>', methods=['POST'])
@login_required
@teacher_required
def edit_schedule(schedule_id):
    """
    تعديل حصة في الجدول
    """
    from flask import jsonify

    schedule = Schedule.query.get_or_404(schedule_id)

    # التحقق من أن الحصة تنتمي للأستاذ الحالي
    if schedule.user_id != current_user.id:
        return jsonify({'error': 'غير مصرح بالوصول'}), 403

    try:
        day_of_week = int(request.form.get('day_of_week'))
        start_hour = int(request.form.get('start_time').split(':')[0])
        start_minute = int(request.form.get('start_time').split(':')[1])
        end_hour = int(request.form.get('end_time').split(':')[0])
        end_minute = int(request.form.get('end_time').split(':')[1])
        subject_id = request.form.get('subject_id')
        level_id = request.form.get('level_id')

        # إنشاء كائنات الوقت
        start_time = time(hour=start_hour, minute=start_minute)
        end_time = time(hour=end_hour, minute=end_minute)

        # التحقق من أن وقت البداية قبل وقت النهاية
        if start_time >= end_time:
            return jsonify({'error': 'يجب أن يكون وقت البداية قبل وقت النهاية'}), 400

        # تحديث بيانات الحصة
        schedule.day_of_week = day_of_week
        schedule.start_time = start_time
        schedule.end_time = end_time
        schedule.subject_id = subject_id
        schedule.level_id = level_id

        db.session.commit()

        return jsonify({'success': True, 'message': 'تم تحديث الحصة بنجاح'})

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': f'حدث خطأ أثناء تحديث الحصة: {str(e)}'}), 500

@teacher_bp.route('/notifications')
@login_required
@teacher_required
def notifications():
    """
    عرض الإشعارات الواردة للأستاذ
    """
    # جلب الإشعارات الواردة من المفتشين مع pagination
    page = request.args.get('page', 1, type=int)
    notifications = InspectorTeacherNotification.query.filter_by(
        receiver_id=current_user.id
    ).order_by(InspectorTeacherNotification.created_at.desc()).paginate(
        page=page, per_page=10, error_out=False
    )

    return render_template('teacher_notifications.html', notifications=notifications)