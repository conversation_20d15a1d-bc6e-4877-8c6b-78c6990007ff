/* مؤثرات التحويم على البطاقات الملونة */
.card.bg-primary, .card.bg-success, .card.bg-warning, .card.bg-danger, .card.bg-info {
    transition: all 0.3s ease;
    cursor: pointer;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.card.bg-primary:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 16px rgba(13, 110, 253, 0.3);
}

.card.bg-success:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 16px rgba(25, 135, 84, 0.3);
}

.card.bg-warning:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 16px rgba(255, 193, 7, 0.3);
}

.card.bg-danger:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 16px rgba(220, 53, 69, 0.3);
}

.card.bg-info:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 16px rgba(13, 202, 240, 0.3);
}

/* مؤثر النبض على الأرقام */
.card:hover .h3 {
    animation: pulse 1s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

/* مؤثرات إضافية للبطاقات */
.card.bg-primary:hover .card-footer,
.card.bg-success:hover .card-footer,
.card.bg-warning:hover .card-footer,
.card.bg-danger:hover .card-footer,
.card.bg-info:hover .card-footer {
    background-color: rgba(255, 255, 255, 0.1);
}

.card:hover .fa-angle-left {
    animation: bounce-left 1s infinite;
}

@keyframes bounce-left {
    0%, 100% {
        transform: translateX(0);
    }
    50% {
        transform: translateX(-5px);
    }
}
