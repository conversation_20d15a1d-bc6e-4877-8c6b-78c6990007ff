# تعليمات تسجيل الدخول - تطبيق Ta9affi

## تم حل مشكلة تسجيل الدخول ✅

تم إصلاح جميع المشاكل المتعلقة بتسجيل الدخول وإنشاء المستخدمين الافتراضيين.

## المستخدمون المتاحون

### 1. حساب المدير (الإدارة)
- **اسم المستخدم:** `admin`
- **كلمة المرور:** `Admin@1234`
- **الدور:** إدارة
- **الصلاحيات:** إدارة النظام، إنشاء المستخدمين، إدارة المحتوى

### 2. حساب المفتش
- **اسم المستخدم:** `inspector`
- **كلمة المرور:** `Inspector#2024`
- **الدور:** مفتش
- **الصلاحيات:** متابعة الأساتذة، مراجعة التقدم، إرسال الإشعارات

### 3. حساب الأستاذ
- **اسم المستخدم:** `teacher`
- **كلمة المرور:** `Teacher@2024`
- **الدور:** أستاذ
- **الصلاحيات:** إدارة البرنامج السنوي، تسجيل التقدم، عرض الجدول

## كيفية تسجيل الدخول

1. **تشغيل التطبيق:**
   ```bash
   python run.py
   ```

2. **فتح المتصفح والانتقال إلى:**
   ```
   http://127.0.0.1:5000/auth/login
   ```

3. **إدخال بيانات الاعتماد:**
   - اختر أحد المستخدمين المذكورين أعلاه
   - أدخل اسم المستخدم وكلمة المرور بدقة (مع مراعاة الأحرف الكبيرة والصغيرة)

4. **الضغط على "تسجيل الدخول"**

## ملاحظات مهمة

- **كلمات المرور حساسة للأحرف الكبيرة والصغيرة**
- **تأكد من إدخال البيانات بدقة**
- **جميع الحسابات نشطة ومتاحة للاستخدام**

## في حالة وجود مشاكل

إذا واجهت أي مشاكل في تسجيل الدخول:

1. **تأكد من تشغيل التطبيق بنجاح**
2. **تحقق من صحة اسم المستخدم وكلمة المرور**
3. **تأكد من أن قاعدة البيانات تحتوي على المستخدمين:**
   ```bash
   python simple_test.py
   ```

## إنشاء مستخدمين جدد

يمكن للمدير إنشاء مستخدمين جدد من خلال:
1. تسجيل الدخول بحساب المدير
2. الانتقال إلى صفحة التسجيل
3. إدخال بيانات المستخدم الجديد

---

## آخر التحديثات

### ✅ تم إصلاح مشكلة القائمة المنسدلة الفارغة للأساتذة (22/06/2025)
- **المشكلة:** في حساب المفتش، القائمة المنسدلة للأساتذة فارغة ولا يمكن اختيار أي أستاذ
- **السبب:** المفتش لا يملك أساتذة مرتبطين به في قاعدة البيانات عبر جدول `inspector_teacher`
- **الحل:**
  - إنشاء سكريبت `add_inspector_teacher_relationship.py`:
    - فحص العلاقات الحالية بين المفتشين والأساتذة
    - إنشاء أساتذة إضافيين للاختبار (`teacher2`, `teacher3`)
    - ربط جميع الأساتذة بالمفتش الافتراضي
    - التحقق من نجاح العمليات
  - تحديث قالب `inspector_notifications.html`:
    - إضافة نوع الإرسال (جميع الأساتذة أو أستاذ محدد)
    - إضافة قائمة الأولوية (عادية، عالية، عاجلة)
    - تحسين عرض الأساتذة مع البريد الإلكتروني
    - إضافة JavaScript لإظهار/إخفاء قائمة الأساتذة
  - تصحيح أسماء الحقول:
    - تغيير `receiver_id` إلى `teacher_id` لتتوافق مع مسار الإرسال
    - إضافة عداد الأساتذة في خيار "جميع الأساتذة"
- **النتيجة:** المفتش لديه الآن 3 أساتذة تحت إشرافه ويمكنه إرسال الإشعارات لهم
- **الحالة:** تم الإصلاح بنجاح

### ✅ تم إصلاح مشكلة اختفاء وظهور صفحة الإشعارات (22/06/2025)
- **المشكلة:** عند النقر على زر "تحديد كمقروء" في صفحة إشعارات المفتش، الصفحة تختفي وتظهر بسرعة كبيرة
- **السبب:** الروابط المباشرة تؤدي إلى إعادة تحميل فوري للصفحة مما يسبب وميض سريع
- **الحل:**
  - استبدال الروابط المباشرة بأزرار JavaScript:
    - تحويل `<a href="...">` إلى `<button class="mark-as-read-btn">`
    - إضافة `data-notification-id` و `data-notification-type` للأزرار
  - إضافة JavaScript متقدم لمعالجة النقر:
    - تأكيد من المستخدم قبل تحديد الإشعار كمقروء
    - تعطيل الزر أثناء المعالجة مع رسالة "جاري التحديث..."
    - استخدام طلبات AJAX بدلاً من إعادة التحميل الفوري
    - إعادة تحميل الصفحة بعد 500ms من نجاح العملية
  - تحديث مسار `mark_notification_read` في main blueprint:
    - دعم طلبات AJAX مع `X-Requested-With: XMLHttpRequest`
    - إرجاع JSON response للطلبات AJAX
    - معالجة أفضل للأخطاء مع رسائل JSON
    - الحفاظ على السلوك العادي للطلبات غير AJAX
- **الحالة:** تم الإصلاح بنجاح

### ✅ تم إصلاح مشكلة CSRF في admin_notifications.html (22/06/2025)
- **المشكلة:** `Bad Request - The CSRF token is missing` عند إرسال إشعار من `/admin/notifications/send`
- **السبب:** قالب `admin_notifications.html` لا يحتوي على رمز CSRF في النموذج
- **الحل:**
  - إضافة رموز CSRF في قالب `admin_notifications.html`:
    - إضافة `{{ csrf_token() }}` في النموذج
    - إضافة `<input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>`
  - التأكد من أن مسار `admin.send_notification` يعمل بشكل صحيح
  - التأكد من تمرير قائمة المفتشين (`inspectors`) للقالب
- **الحالة:** تم الإصلاح بنجاح

### ✅ تم إصلاح مشكلة admin_notifications undefined (22/06/2025)
- **المشكلة:** `jinja2.exceptions.UndefinedError: 'admin_notifications' is undefined`
- **السبب:** قالب `inspector_notifications.html` يستخدم متغيرات `admin_notifications` و `sent_notifications` لكن مسار `inspector.notifications()` لا يمررها
- **الحل:**
  - تحديث مسار `inspector.notifications()` في inspector blueprint:
    - إضافة pagination للإشعارات الواردة من الإدارة (`admin_notifications`)
    - إضافة pagination للإشعارات المرسلة للأساتذة (`sent_notifications`)
    - تمرير قائمة الأساتذة تحت الإشراف (`teachers`)
    - استخدام `AdminInspectorNotification.query.filter_by(receiver_id=current_user.id)`
    - استخدام `InspectorTeacherNotification.query.filter_by(sender_id=current_user.id)`
  - تمرير جميع المتغيرات المطلوبة للقالب:
    - `admin_notifications`: الإشعارات الواردة من الإدارة مع pagination
    - `sent_notifications`: الإشعارات المرسلة للأساتذة مع pagination
    - `teachers`: قائمة الأساتذة تحت الإشراف
- **الحالة:** تم الإصلاح بنجاح

### ✅ تم إصلاح مشكلة مسار send_inspector_notification (22/06/2025)
- **المشكلة:** `werkzeug.routing.exceptions.BuildError: Could not build url for endpoint 'send_inspector_notification'`
- **السبب:** استدعاء مسار غير موجود في inspector blueprint في قالب `inspector_notifications.html`
- **الحل:**
  - إضافة مسار `send_notification_to_teachers` في inspector blueprint:
    - دعم إرسال الإشعارات لجميع الأساتذة أو لأستاذ محدد
    - إضافة مستوى الأولوية للإشعارات (عادية، عالية، عاجلة)
    - التحقق من صلاحيات المفتش للإرسال للأساتذة تحت إشرافه
  - تصحيح جميع استدعاءات URL في `inspector_notifications.html`:
    - `url_for('send_inspector_notification')` → `url_for('inspector.send_notification_to_teachers')`
    - `url_for('inspector_notifications', ...)` → `url_for('inspector.notifications', ...)`
    - `url_for('mark_notification_read', ...)` → `url_for('main.mark_notification_read', ...)`
  - إضافة مسار `mark_notification_read` في main blueprint لتحديد الإشعارات كمقروءة
  - إضافة رموز CSRF للنموذج الجديد
- **الحالة:** تم الإصلاح بنجاح

### ✅ تم إصلاح مشكلة رموز CSRF المفقودة (22/06/2025)
- **المشكلة:** `Bad Request - The CSRF token is missing` عند إرسال الإشعارات والنماذج
- **السبب:** عدم وجود رموز CSRF في النماذج وطلبات AJAX
- **الحل:**
  - إضافة رموز CSRF لجميع النماذج:
    - `admin_inspector_notifications.html`: نموذج إرسال الإشعارات
    - `inspector_teacher_notifications.html`: نموذج إرسال الإشعارات للأساتذة
    - `manage_schedule.html`: نماذج إضافة وحذف الحصص
    - `teaching_program.html`: نموذج إضافة التقدم
  - إضافة رموز CSRF لطلبات AJAX:
    - إضافة `X-CSRFToken` في headers
    - إضافة `csrf_token` في body للطلبات
  - استخدام `{{ csrf_token() }}` و `<input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>`
- **الحالة:** تم الإصلاح بنجاح

### ✅ تم إصلاح مشكلة مسارات الإشعارات والتقدم (22/06/2025)
- **المشكلة:** `werkzeug.routing.exceptions.BuildError: Could not build url for endpoint 'teacher_notifications'`
- **السبب:** استدعاء مسارات بدون blueprint prefix في قالب `teacher_dashboard.html`
- **الحل:**
  - تصحيح جميع استدعاءات URL في `teacher_dashboard.html`:
    - `url_for('teacher_notifications')` → `url_for('teacher.notifications')`
    - `url_for('mark_notification_read', ...)` → `url_for('main.mark_notification_read', ...)`
    - `url_for('prepare_lesson', ...)` → `url_for('main.prepare_lesson', ...)`
    - `url_for('edit_progress', ...)` → `url_for('main.edit_progress', ...)`
    - `url_for('teaching_program')` → `url_for('main.teaching_program')`
  - تصحيح مسار حذف التقدم في JavaScript: `/progress/delete/` → `/delete_progress/`
- **الحالة:** تم الإصلاح بنجاح

### ✅ تم إصلاح مشكلة مسارات الجدول الدراسي (22/06/2025)
- **المشكلة:** `werkzeug.routing.exceptions.BuildError: Could not build url for endpoint 'manage_schedule'`
- **السبب:** استدعاء مسارات بدون blueprint prefix في قوالب إدارة الجدول
- **الحل:**
  - تصحيح استدعاءات URL في `teacher_dashboard.html` و `manage_schedule.html`:
    - `url_for('manage_schedule')` → `url_for('teacher.manage_schedule')`
    - `url_for('add_schedule')` → `url_for('teacher.add_schedule')`
  - إضافة مسارات API مفقودة في `teacher.py`:
    - `/teacher/api/schedule/<id>`: للحصول على تفاصيل حصة
    - `/teacher/edit_schedule/<id>`: لتعديل حصة
  - تحديث مسارات JavaScript في القالب لتستخدم المسارات الصحيحة
- **الحالة:** تم الإصلاح بنجاح

### ✅ تم إصلاح مشكلة المتغيرات المفقودة في لوحة المفتش (22/06/2025)
- **المشكلة:** `jinja2.exceptions.UndefinedError: 'teacher_progress' is undefined`
- **السبب:** عدم تمرير متغير `teacher_progress` المطلوب في قالب `inspector_dashboard.html`
- **الحل:**
  - إضافة حساب تقدم كل أستاذ على حدة في `inspector.py`:
    - `teacher_progress`: قاموس يحتوي على تقدم كل أستاذ مع معرفه
    - لكل أستاذ: نسبة الإكمال، إحصائيات مفصلة، أحدث 5 إدخالات
    - `overall_completion_rate`: نسبة الإكمال الإجمالية لجميع الأساتذة
  - تحسين عرض البيانات في القالب مع مودالات تفاعلية لعرض تفاصيل كل أستاذ
- **الحالة:** تم الإصلاح بنجاح

### ✅ تم إصلاح مشكلة المتغيرات غير المعرفة في لوحات التحكم (22/06/2025)
- **المشكلة:** `jinja2.exceptions.UndefinedError: 'progress_stats' is undefined`
- **السبب:** عدم تمرير متغيرات الإحصائيات المطلوبة في قوالب لوحات التحكم
- **الحل:**
  - تحديث `inspector.py` لحساب وتمرير إحصائيات التقدم لجميع الأساتذة تحت الإشراف:
    - `progress_stats`: إحصائيات التقدم الإجمالية
    - `level_stats`: إحصائيات حسب المستوى التعليمي
    - `completion_rate`: نسبة الإكمال الإجمالية
  - تحديث `teacher.py` لحساب وتمرير إحصائيات مفصلة:
    - `progress_stats`: إحصائيات التقدم الشخصية
    - `level_stats`: إحصائيات حسب المستوى
    - `subject_stats`: إحصائيات حسب المادة الدراسية
    - `progress_entries`: قائمة جميع إدخالات التقدم
- **الحالة:** تم الإصلاح بنجاح

### ✅ تم إصلاح مشكلة المتغير غير المعرف (22/06/2025)
- **المشكلة:** `jinja2.exceptions.UndefinedError: 'unread_notifications_count' is undefined`
- **السبب:** استخدام متغير `unread_notifications_count` في قالب `base.html` بدون تعريفه عالمياً
- **الحل:**
  - إضافة context processor في `app/__init__.py` لجعل المتغير متاحاً في جميع القوالب
  - حساب عدد الإشعارات غير المقروءة حسب دور المستخدم:
    - للمفتش: إشعارات من الإدارة (`AdminInspectorNotification`)
    - للأستاذ: إشعارات من المفتشين (`InspectorTeacherNotification`)
    - للمدير: 0 (لا يتلقى إشعارات حالياً)
  - تحديث قالب `base.html` للتحقق من وجود المتغير قبل استخدامه
- **الحالة:** تم الإصلاح بنجاح

### ✅ تم إصلاح مشكلة القوالب المفقودة الأخيرة (22/06/2025)
- **المشكلة:** `jinja2.exceptions.TemplateNotFound: profile.html`
- **السبب:** عدم وجود قوالب مطلوبة في التطبيق
- **الحل:**
  - إنشاء قالب `profile.html` للملف الشخصي للمستخدم
  - إنشاء مجلد `templates/notifications/` والقوالب المطلوبة:
    - `admin_inspector_notifications.html` - الإشعارات بين الإدارة والمفتشين
    - `inspector_teacher_notifications.html` - الإشعارات بين المفتشين والأساتذة
  - تصميم واجهات مستخدم متكاملة لكل نوع من الإشعارات
- **الحالة:** تم الإصلاح بنجاح

### ✅ تم إصلاح مشكلة level_databases undefined (22/06/2025)
- **المشكلة:** `jinja2.exceptions.UndefinedError: 'level_databases' is undefined`
- **السبب:** عدم تمرير المتغيرات المطلوبة (levels, level_databases) إلى قالب teaching_program.html
- **الحل:**
  - تحديث دالة teaching_program في main.py لجلب المستويات التعليمية وقواعد البيانات النشطة
  - إضافة imports للنماذج المطلوبة (EducationalLevel, LevelDatabase)
  - تصحيح جميع مسارات teacher blueprint لإزالة prefix المكرر
  - تصحيح استدعاءات URL في teacher blueprint
- **الحالة:** تم الإصلاح بنجاح

### ✅ تم إصلاح مشكلة URL الأخيرة (22/06/2025)
- **المشكلة:** `werkzeug.routing.exceptions.BuildError: Could not build url for endpoint 'manage_databases'`
- **السبب:** استدعاء `url_for('manage_databases')` بدلاً من `url_for('admin.manage_databases')` في عدة قوالب
- **الحل:**
  - تصحيح جميع استدعاءات URL في القوالب لتستخدم blueprint prefix الصحيح
  - إضافة المسارات المفقودة إلى admin blueprint (view_database, edit_database, export_database_data, cleanup_inactive_levels)
  - تصحيح استدعاءات URL في manage_level_databases.html, view_database.html, edit_database.html, teaching_program.html, teacher_notifications.html
- **الحالة:** تم الإصلاح بنجاح

### ✅ تم إصلاح مشكلة URL المفقودة النهائية (22/06/2025)
- **المشكلة:** `werkzeug.routing.exceptions.BuildError: Could not build url for endpoint 'dashboard'`
- **السبب:** استدعاء `url_for('dashboard')` بدلاً من `url_for('main.dashboard')` ومسارات blueprints مكررة
- **الحل:**
  - تصحيح جميع استدعاءات `url_for('dashboard')` إلى `url_for('main.dashboard')`
  - إزالة `/admin/` و `/inspector/` من مسارات blueprints لأنها مضافة تلقائياً عند التسجيل
  - تصحيح جميع مسارات admin و inspector blueprints
- **الحالة:** تم الإصلاح بنجاح

### ✅ تم إصلاح مشكلة URL المفقودة (22/06/2025)
- **المشكلة:** `werkzeug.routing.exceptions.BuildError: Could not build url for endpoint 'admin.notifications'`
- **السبب:** عدم وجود مسار `notifications` في admin blueprint
- **الحل:** إضافة مسارات الإشعارات وإدارة قواعد البيانات إلى admin blueprint وتصحيح استدعاءات URL في القوالب
- **الحالة:** تم الإصلاح بنجاح

### ✅ تم إصلاح مشكلة القوالب المفقودة (22/06/2025)
- **المشكلة:** `jinja2.exceptions.TemplateNotFound: inspector/dashboard.html`
- **السبب:** استدعاء قوالب بمسارات خاطئة (مثل `inspector/dashboard.html` بدلاً من `inspector_dashboard.html`)
- **الحل:** تصحيح جميع استدعاءات `render_template` في ملفات blueprints
- **الحالة:** تم الإصلاح بنجاح

### ✅ تم إصلاح مشكلة session.regenerate (22/06/2025)
- **المشكلة:** `AttributeError: 'SecureCookieSession' object has no attribute 'regenerate'`
- **الحل:** استبدال `session.regenerate()` بـ `session.permanent = True` و `session.clear()`
- **الحالة:** تم الإصلاح بنجاح

### ✅ تم إصلاح مشكلة is_active المفقودة
- **المشكلة:** `no such column: user.is_active`
- **الحل:** إضافة عمود `is_active` إلى نموذج User وإعادة إنشاء قاعدة البيانات
- **الحالة:** تم الإصلاح بنجاح

### ✅ تم إنشاء المستخدمين الافتراضيين
- **المشكلة:** عدم وجود مستخدمين للاختبار
- **الحل:** إنشاء 3 مستخدمين افتراضيين بكلمات مرور قوية
- **الحالة:** تم الإنجاز بنجاح

---

**جميع المشاكل تم حلها! التطبيق جاهز للاستخدام 🎉**
