# تعليمات تسجيل الدخول - تطبيق Ta9affi

## تم حل مشكلة تسجيل الدخول ✅

تم إصلاح جميع المشاكل المتعلقة بتسجيل الدخول وإنشاء المستخدمين الافتراضيين.

## المستخدمون المتاحون

### 1. حساب المدير (الإدارة)
- **اسم المستخدم:** `admin`
- **كلمة المرور:** `Admin@1234`
- **الدور:** إدارة
- **الصلاحيات:** إدارة النظام، إنشاء المستخدمين، إدارة المحتوى

### 2. حساب المفتش
- **اسم المستخدم:** `inspector`
- **كلمة المرور:** `Inspector#2024`
- **الدور:** مفتش
- **الصلاحيات:** متابعة الأساتذة، مراجعة التقدم، إرسال الإشعارات

### 3. حساب الأستاذ
- **اسم المستخدم:** `teacher`
- **كلمة المرور:** `Teacher@2024`
- **الدور:** أستاذ
- **الصلاحيات:** إدارة البرنامج السنوي، تسجيل التقدم، عرض الجدول

## كيفية تسجيل الدخول

1. **تشغيل التطبيق:**
   ```bash
   python run.py
   ```

2. **فتح المتصفح والانتقال إلى:**
   ```
   http://127.0.0.1:5000/auth/login
   ```

3. **إدخال بيانات الاعتماد:**
   - اختر أحد المستخدمين المذكورين أعلاه
   - أدخل اسم المستخدم وكلمة المرور بدقة (مع مراعاة الأحرف الكبيرة والصغيرة)

4. **الضغط على "تسجيل الدخول"**

## ملاحظات مهمة

- **كلمات المرور حساسة للأحرف الكبيرة والصغيرة**
- **تأكد من إدخال البيانات بدقة**
- **جميع الحسابات نشطة ومتاحة للاستخدام**

## في حالة وجود مشاكل

إذا واجهت أي مشاكل في تسجيل الدخول:

1. **تأكد من تشغيل التطبيق بنجاح**
2. **تحقق من صحة اسم المستخدم وكلمة المرور**
3. **تأكد من أن قاعدة البيانات تحتوي على المستخدمين:**
   ```bash
   python simple_test.py
   ```

## إنشاء مستخدمين جدد

يمكن للمدير إنشاء مستخدمين جدد من خلال:
1. تسجيل الدخول بحساب المدير
2. الانتقال إلى صفحة التسجيل
3. إدخال بيانات المستخدم الجديد

---

## آخر التحديثات

### ✅ تم إصلاح مشكلة level_databases undefined (22/06/2025)
- **المشكلة:** `jinja2.exceptions.UndefinedError: 'level_databases' is undefined`
- **السبب:** عدم تمرير المتغيرات المطلوبة (levels, level_databases) إلى قالب teaching_program.html
- **الحل:**
  - تحديث دالة teaching_program في main.py لجلب المستويات التعليمية وقواعد البيانات النشطة
  - إضافة imports للنماذج المطلوبة (EducationalLevel, LevelDatabase)
  - تصحيح جميع مسارات teacher blueprint لإزالة prefix المكرر
  - تصحيح استدعاءات URL في teacher blueprint
- **الحالة:** تم الإصلاح بنجاح

### ✅ تم إصلاح مشكلة URL الأخيرة (22/06/2025)
- **المشكلة:** `werkzeug.routing.exceptions.BuildError: Could not build url for endpoint 'manage_databases'`
- **السبب:** استدعاء `url_for('manage_databases')` بدلاً من `url_for('admin.manage_databases')` في عدة قوالب
- **الحل:**
  - تصحيح جميع استدعاءات URL في القوالب لتستخدم blueprint prefix الصحيح
  - إضافة المسارات المفقودة إلى admin blueprint (view_database, edit_database, export_database_data, cleanup_inactive_levels)
  - تصحيح استدعاءات URL في manage_level_databases.html, view_database.html, edit_database.html, teaching_program.html, teacher_notifications.html
- **الحالة:** تم الإصلاح بنجاح

### ✅ تم إصلاح مشكلة URL المفقودة النهائية (22/06/2025)
- **المشكلة:** `werkzeug.routing.exceptions.BuildError: Could not build url for endpoint 'dashboard'`
- **السبب:** استدعاء `url_for('dashboard')` بدلاً من `url_for('main.dashboard')` ومسارات blueprints مكررة
- **الحل:**
  - تصحيح جميع استدعاءات `url_for('dashboard')` إلى `url_for('main.dashboard')`
  - إزالة `/admin/` و `/inspector/` من مسارات blueprints لأنها مضافة تلقائياً عند التسجيل
  - تصحيح جميع مسارات admin و inspector blueprints
- **الحالة:** تم الإصلاح بنجاح

### ✅ تم إصلاح مشكلة URL المفقودة (22/06/2025)
- **المشكلة:** `werkzeug.routing.exceptions.BuildError: Could not build url for endpoint 'admin.notifications'`
- **السبب:** عدم وجود مسار `notifications` في admin blueprint
- **الحل:** إضافة مسارات الإشعارات وإدارة قواعد البيانات إلى admin blueprint وتصحيح استدعاءات URL في القوالب
- **الحالة:** تم الإصلاح بنجاح

### ✅ تم إصلاح مشكلة القوالب المفقودة (22/06/2025)
- **المشكلة:** `jinja2.exceptions.TemplateNotFound: inspector/dashboard.html`
- **السبب:** استدعاء قوالب بمسارات خاطئة (مثل `inspector/dashboard.html` بدلاً من `inspector_dashboard.html`)
- **الحل:** تصحيح جميع استدعاءات `render_template` في ملفات blueprints
- **الحالة:** تم الإصلاح بنجاح

### ✅ تم إصلاح مشكلة session.regenerate (22/06/2025)
- **المشكلة:** `AttributeError: 'SecureCookieSession' object has no attribute 'regenerate'`
- **الحل:** استبدال `session.regenerate()` بـ `session.permanent = True` و `session.clear()`
- **الحالة:** تم الإصلاح بنجاح

### ✅ تم إصلاح مشكلة is_active المفقودة
- **المشكلة:** `no such column: user.is_active`
- **الحل:** إضافة عمود `is_active` إلى نموذج User وإعادة إنشاء قاعدة البيانات
- **الحالة:** تم الإصلاح بنجاح

### ✅ تم إنشاء المستخدمين الافتراضيين
- **المشكلة:** عدم وجود مستخدمين للاختبار
- **الحل:** إنشاء 3 مستخدمين افتراضيين بكلمات مرور قوية
- **الحالة:** تم الإنجاز بنجاح

---

**جميع المشاكل تم حلها! التطبيق جاهز للاستخدام 🎉**
