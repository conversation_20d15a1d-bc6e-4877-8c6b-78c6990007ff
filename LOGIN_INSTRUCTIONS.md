# تعليمات تسجيل الدخول - تطبيق Ta9affi

## تم حل مشكلة تسجيل الدخول ✅

تم إصلاح جميع المشاكل المتعلقة بتسجيل الدخول وإنشاء المستخدمين الافتراضيين.

## المستخدمون المتاحون

### 1. حساب المدير (الإدارة)
- **اسم المستخدم:** `admin`
- **كلمة المرور:** `Admin@1234`
- **الدور:** إدارة
- **الصلاحيات:** إدارة النظام، إنشاء المستخدمين، إدارة المحتوى

### 2. حساب المفتش
- **اسم المستخدم:** `inspector`
- **كلمة المرور:** `Inspector#2024`
- **الدور:** مفتش
- **الصلاحيات:** متابعة الأساتذة، مراجعة التقدم، إرسال الإشعارات

### 3. حساب الأستاذ
- **اسم المستخدم:** `teacher`
- **كلمة المرور:** `Teacher@2024`
- **الدور:** أستاذ
- **الصلاحيات:** إدارة البرنامج السنوي، تسجيل التقدم، عرض الجدول

## كيفية تسجيل الدخول

1. **تشغيل التطبيق:**
   ```bash
   python run.py
   ```

2. **فتح المتصفح والانتقال إلى:**
   ```
   http://127.0.0.1:5000/auth/login
   ```

3. **إدخال بيانات الاعتماد:**
   - اختر أحد المستخدمين المذكورين أعلاه
   - أدخل اسم المستخدم وكلمة المرور بدقة (مع مراعاة الأحرف الكبيرة والصغيرة)

4. **الضغط على "تسجيل الدخول"**

## ملاحظات مهمة

- **كلمات المرور حساسة للأحرف الكبيرة والصغيرة**
- **تأكد من إدخال البيانات بدقة**
- **جميع الحسابات نشطة ومتاحة للاستخدام**

## في حالة وجود مشاكل

إذا واجهت أي مشاكل في تسجيل الدخول:

1. **تأكد من تشغيل التطبيق بنجاح**
2. **تحقق من صحة اسم المستخدم وكلمة المرور**
3. **تأكد من أن قاعدة البيانات تحتوي على المستخدمين:**
   ```bash
   python simple_test.py
   ```

## إنشاء مستخدمين جدد

يمكن للمدير إنشاء مستخدمين جدد من خلال:
1. تسجيل الدخول بحساب المدير
2. الانتقال إلى صفحة التسجيل
3. إدخال بيانات المستخدم الجديد

---

## آخر التحديثات

### ✅ تم إصلاح مشكلة مسارات الإشعارات والتقدم (22/06/2025)
- **المشكلة:** `werkzeug.routing.exceptions.BuildError: Could not build url for endpoint 'teacher_notifications'`
- **السبب:** استدعاء مسارات بدون blueprint prefix في قالب `teacher_dashboard.html`
- **الحل:**
  - تصحيح جميع استدعاءات URL في `teacher_dashboard.html`:
    - `url_for('teacher_notifications')` → `url_for('teacher.notifications')`
    - `url_for('mark_notification_read', ...)` → `url_for('main.mark_notification_read', ...)`
    - `url_for('prepare_lesson', ...)` → `url_for('main.prepare_lesson', ...)`
    - `url_for('edit_progress', ...)` → `url_for('main.edit_progress', ...)`
    - `url_for('teaching_program')` → `url_for('main.teaching_program')`
  - تصحيح مسار حذف التقدم في JavaScript: `/progress/delete/` → `/delete_progress/`
- **الحالة:** تم الإصلاح بنجاح

### ✅ تم إصلاح مشكلة مسارات الجدول الدراسي (22/06/2025)
- **المشكلة:** `werkzeug.routing.exceptions.BuildError: Could not build url for endpoint 'manage_schedule'`
- **السبب:** استدعاء مسارات بدون blueprint prefix في قوالب إدارة الجدول
- **الحل:**
  - تصحيح استدعاءات URL في `teacher_dashboard.html` و `manage_schedule.html`:
    - `url_for('manage_schedule')` → `url_for('teacher.manage_schedule')`
    - `url_for('add_schedule')` → `url_for('teacher.add_schedule')`
  - إضافة مسارات API مفقودة في `teacher.py`:
    - `/teacher/api/schedule/<id>`: للحصول على تفاصيل حصة
    - `/teacher/edit_schedule/<id>`: لتعديل حصة
  - تحديث مسارات JavaScript في القالب لتستخدم المسارات الصحيحة
- **الحالة:** تم الإصلاح بنجاح

### ✅ تم إصلاح مشكلة المتغيرات المفقودة في لوحة المفتش (22/06/2025)
- **المشكلة:** `jinja2.exceptions.UndefinedError: 'teacher_progress' is undefined`
- **السبب:** عدم تمرير متغير `teacher_progress` المطلوب في قالب `inspector_dashboard.html`
- **الحل:**
  - إضافة حساب تقدم كل أستاذ على حدة في `inspector.py`:
    - `teacher_progress`: قاموس يحتوي على تقدم كل أستاذ مع معرفه
    - لكل أستاذ: نسبة الإكمال، إحصائيات مفصلة، أحدث 5 إدخالات
    - `overall_completion_rate`: نسبة الإكمال الإجمالية لجميع الأساتذة
  - تحسين عرض البيانات في القالب مع مودالات تفاعلية لعرض تفاصيل كل أستاذ
- **الحالة:** تم الإصلاح بنجاح

### ✅ تم إصلاح مشكلة المتغيرات غير المعرفة في لوحات التحكم (22/06/2025)
- **المشكلة:** `jinja2.exceptions.UndefinedError: 'progress_stats' is undefined`
- **السبب:** عدم تمرير متغيرات الإحصائيات المطلوبة في قوالب لوحات التحكم
- **الحل:**
  - تحديث `inspector.py` لحساب وتمرير إحصائيات التقدم لجميع الأساتذة تحت الإشراف:
    - `progress_stats`: إحصائيات التقدم الإجمالية
    - `level_stats`: إحصائيات حسب المستوى التعليمي
    - `completion_rate`: نسبة الإكمال الإجمالية
  - تحديث `teacher.py` لحساب وتمرير إحصائيات مفصلة:
    - `progress_stats`: إحصائيات التقدم الشخصية
    - `level_stats`: إحصائيات حسب المستوى
    - `subject_stats`: إحصائيات حسب المادة الدراسية
    - `progress_entries`: قائمة جميع إدخالات التقدم
- **الحالة:** تم الإصلاح بنجاح

### ✅ تم إصلاح مشكلة المتغير غير المعرف (22/06/2025)
- **المشكلة:** `jinja2.exceptions.UndefinedError: 'unread_notifications_count' is undefined`
- **السبب:** استخدام متغير `unread_notifications_count` في قالب `base.html` بدون تعريفه عالمياً
- **الحل:**
  - إضافة context processor في `app/__init__.py` لجعل المتغير متاحاً في جميع القوالب
  - حساب عدد الإشعارات غير المقروءة حسب دور المستخدم:
    - للمفتش: إشعارات من الإدارة (`AdminInspectorNotification`)
    - للأستاذ: إشعارات من المفتشين (`InspectorTeacherNotification`)
    - للمدير: 0 (لا يتلقى إشعارات حالياً)
  - تحديث قالب `base.html` للتحقق من وجود المتغير قبل استخدامه
- **الحالة:** تم الإصلاح بنجاح

### ✅ تم إصلاح مشكلة القوالب المفقودة الأخيرة (22/06/2025)
- **المشكلة:** `jinja2.exceptions.TemplateNotFound: profile.html`
- **السبب:** عدم وجود قوالب مطلوبة في التطبيق
- **الحل:**
  - إنشاء قالب `profile.html` للملف الشخصي للمستخدم
  - إنشاء مجلد `templates/notifications/` والقوالب المطلوبة:
    - `admin_inspector_notifications.html` - الإشعارات بين الإدارة والمفتشين
    - `inspector_teacher_notifications.html` - الإشعارات بين المفتشين والأساتذة
  - تصميم واجهات مستخدم متكاملة لكل نوع من الإشعارات
- **الحالة:** تم الإصلاح بنجاح

### ✅ تم إصلاح مشكلة level_databases undefined (22/06/2025)
- **المشكلة:** `jinja2.exceptions.UndefinedError: 'level_databases' is undefined`
- **السبب:** عدم تمرير المتغيرات المطلوبة (levels, level_databases) إلى قالب teaching_program.html
- **الحل:**
  - تحديث دالة teaching_program في main.py لجلب المستويات التعليمية وقواعد البيانات النشطة
  - إضافة imports للنماذج المطلوبة (EducationalLevel, LevelDatabase)
  - تصحيح جميع مسارات teacher blueprint لإزالة prefix المكرر
  - تصحيح استدعاءات URL في teacher blueprint
- **الحالة:** تم الإصلاح بنجاح

### ✅ تم إصلاح مشكلة URL الأخيرة (22/06/2025)
- **المشكلة:** `werkzeug.routing.exceptions.BuildError: Could not build url for endpoint 'manage_databases'`
- **السبب:** استدعاء `url_for('manage_databases')` بدلاً من `url_for('admin.manage_databases')` في عدة قوالب
- **الحل:**
  - تصحيح جميع استدعاءات URL في القوالب لتستخدم blueprint prefix الصحيح
  - إضافة المسارات المفقودة إلى admin blueprint (view_database, edit_database, export_database_data, cleanup_inactive_levels)
  - تصحيح استدعاءات URL في manage_level_databases.html, view_database.html, edit_database.html, teaching_program.html, teacher_notifications.html
- **الحالة:** تم الإصلاح بنجاح

### ✅ تم إصلاح مشكلة URL المفقودة النهائية (22/06/2025)
- **المشكلة:** `werkzeug.routing.exceptions.BuildError: Could not build url for endpoint 'dashboard'`
- **السبب:** استدعاء `url_for('dashboard')` بدلاً من `url_for('main.dashboard')` ومسارات blueprints مكررة
- **الحل:**
  - تصحيح جميع استدعاءات `url_for('dashboard')` إلى `url_for('main.dashboard')`
  - إزالة `/admin/` و `/inspector/` من مسارات blueprints لأنها مضافة تلقائياً عند التسجيل
  - تصحيح جميع مسارات admin و inspector blueprints
- **الحالة:** تم الإصلاح بنجاح

### ✅ تم إصلاح مشكلة URL المفقودة (22/06/2025)
- **المشكلة:** `werkzeug.routing.exceptions.BuildError: Could not build url for endpoint 'admin.notifications'`
- **السبب:** عدم وجود مسار `notifications` في admin blueprint
- **الحل:** إضافة مسارات الإشعارات وإدارة قواعد البيانات إلى admin blueprint وتصحيح استدعاءات URL في القوالب
- **الحالة:** تم الإصلاح بنجاح

### ✅ تم إصلاح مشكلة القوالب المفقودة (22/06/2025)
- **المشكلة:** `jinja2.exceptions.TemplateNotFound: inspector/dashboard.html`
- **السبب:** استدعاء قوالب بمسارات خاطئة (مثل `inspector/dashboard.html` بدلاً من `inspector_dashboard.html`)
- **الحل:** تصحيح جميع استدعاءات `render_template` في ملفات blueprints
- **الحالة:** تم الإصلاح بنجاح

### ✅ تم إصلاح مشكلة session.regenerate (22/06/2025)
- **المشكلة:** `AttributeError: 'SecureCookieSession' object has no attribute 'regenerate'`
- **الحل:** استبدال `session.regenerate()` بـ `session.permanent = True` و `session.clear()`
- **الحالة:** تم الإصلاح بنجاح

### ✅ تم إصلاح مشكلة is_active المفقودة
- **المشكلة:** `no such column: user.is_active`
- **الحل:** إضافة عمود `is_active` إلى نموذج User وإعادة إنشاء قاعدة البيانات
- **الحالة:** تم الإصلاح بنجاح

### ✅ تم إنشاء المستخدمين الافتراضيين
- **المشكلة:** عدم وجود مستخدمين للاختبار
- **الحل:** إنشاء 3 مستخدمين افتراضيين بكلمات مرور قوية
- **الحالة:** تم الإنجاز بنجاح

---

**جميع المشاكل تم حلها! التطبيق جاهز للاستخدام 🎉**
