"""
سكريبت لإعادة تعيين قاعدة البيانات بالكامل
يحذف قاعدة البيانات الحالية ويعيد إنشائها من الصفر
"""

from app import app, db
import os
import shutil
from seed_data import seed_users, seed_educational_data, seed_level_databases

def reset_database():
    """حذف قاعدة البيانات الحالية وإعادة إنشائها"""
    db_path = os.path.join(app.root_path, 'ta9affi.db')
    
    # حذف قاعدة البيانات إذا كانت موجودة
    if os.path.exists(db_path):
        print(f"حذف قاعدة البيانات الحالية: {db_path}")
        os.remove(db_path)
    
    # حذف مجلد البيانات إذا كان موجوداً
    data_dir = os.path.join(app.root_path, 'data')
    if os.path.exists(data_dir):
        print(f"حذف مجلد البيانات: {data_dir}")
        shutil.rmtree(data_dir)
    
    # إنشاء مجلد البيانات
    os.makedirs(data_dir, exist_ok=True)
    
    # إنشاء قاعدة البيانات الجديدة
    print("إنشاء قاعدة البيانات الجديدة...")
    with app.app_context():
        db.create_all()
        
        # إضافة البيانات التجريبية
        print("إضافة المستخدمين التجريبيين...")
        seed_users()
        
        print("إضافة البيانات التعليمية الأساسية...")
        seed_educational_data()
        
        print("إضافة قواعد البيانات المنفصلة للمستويات...")
        seed_level_databases()
    
    print("تم إعادة تعيين قاعدة البيانات بنجاح!")

if __name__ == "__main__":
    reset_database()
