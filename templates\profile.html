{% extends 'base.html' %}

{% block title %}الملف الشخصي - Ta9affi{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-8 mx-auto">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-user me-2"></i>
                        الملف الشخصي
                    </h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- معلومات المستخدم -->
                        <div class="col-md-6">
                            <div class="card border-0 bg-light">
                                <div class="card-header bg-info text-white">
                                    <h5 class="mb-0">
                                        <i class="fas fa-info-circle me-1"></i>
                                        المعلومات الشخصية
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">اسم المستخدم:</label>
                                        <p class="form-control-plaintext">{{ current_user.username }}</p>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">البريد الإلكتروني:</label>
                                        <p class="form-control-plaintext">{{ current_user.email or 'غير محدد' }}</p>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">الدور:</label>
                                        <p class="form-control-plaintext">
                                            {% if current_user.role == 'admin' %}
                                                <span class="badge bg-danger">مدير</span>
                                            {% elif current_user.role == 'inspector' %}
                                                <span class="badge bg-warning">مفتش</span>
                                            {% elif current_user.role == 'teacher' %}
                                                <span class="badge bg-success">أستاذ</span>
                                            {% else %}
                                                <span class="badge bg-secondary">{{ current_user.role }}</span>
                                            {% endif %}
                                        </p>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">الحالة:</label>
                                        <p class="form-control-plaintext">
                                            {% if current_user.is_active %}
                                                <span class="badge bg-success">نشط</span>
                                            {% else %}
                                                <span class="badge bg-danger">غير نشط</span>
                                            {% endif %}
                                        </p>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">تاريخ الإنشاء:</label>
                                        <p class="form-control-plaintext">{{ current_user.created_at.strftime('%Y-%m-%d %H:%M') if current_user.created_at else 'غير محدد' }}</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- إحصائيات المستخدم -->
                        <div class="col-md-6">
                            <div class="card border-0 bg-light">
                                <div class="card-header bg-success text-white">
                                    <h5 class="mb-0">
                                        <i class="fas fa-chart-bar me-1"></i>
                                        الإحصائيات
                                    </h5>
                                </div>
                                <div class="card-body">
                                    {% if current_user.role == 'admin' %}
                                        <div class="mb-3">
                                            <label class="form-label fw-bold">عدد المستخدمين:</label>
                                            <p class="form-control-plaintext">
                                                <span class="badge bg-primary">قيد التطوير</span>
                                            </p>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label fw-bold">عدد قواعد البيانات:</label>
                                            <p class="form-control-plaintext">
                                                <span class="badge bg-primary">قيد التطوير</span>
                                            </p>
                                        </div>
                                    {% elif current_user.role == 'inspector' %}
                                        <div class="mb-3">
                                            <label class="form-label fw-bold">عدد الأساتذة المشرف عليهم:</label>
                                            <p class="form-control-plaintext">
                                                <span class="badge bg-primary">قيد التطوير</span>
                                            </p>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label fw-bold">عدد الإشعارات المرسلة:</label>
                                            <p class="form-control-plaintext">
                                                <span class="badge bg-primary">قيد التطوير</span>
                                            </p>
                                        </div>
                                    {% elif current_user.role == 'teacher' %}
                                        <div class="mb-3">
                                            <label class="form-label fw-bold">عدد الدروس المحضرة:</label>
                                            <p class="form-control-plaintext">
                                                <span class="badge bg-primary">قيد التطوير</span>
                                            </p>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label fw-bold">التقدم في البرنامج:</label>
                                            <p class="form-control-plaintext">
                                                <span class="badge bg-primary">قيد التطوير</span>
                                            </p>
                                        </div>
                                    {% endif %}
                                    
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">آخر تسجيل دخول:</label>
                                        <p class="form-control-plaintext">
                                            <span class="badge bg-info">قيد التطوير</span>
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- أزرار الإجراءات -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card border-0 bg-light">
                                <div class="card-header bg-warning text-dark">
                                    <h5 class="mb-0">
                                        <i class="fas fa-cogs me-1"></i>
                                        إعدادات الحساب
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="d-flex flex-wrap gap-2">
                                        <button class="btn btn-primary" onclick="alert('ميزة تعديل الملف الشخصي قيد التطوير')">
                                            <i class="fas fa-edit me-1"></i>
                                            تعديل الملف الشخصي
                                        </button>
                                        <button class="btn btn-warning" onclick="alert('ميزة تغيير كلمة المرور قيد التطوير')">
                                            <i class="fas fa-key me-1"></i>
                                            تغيير كلمة المرور
                                        </button>
                                        <button class="btn btn-info" onclick="alert('ميزة إعدادات الإشعارات قيد التطوير')">
                                            <i class="fas fa-bell me-1"></i>
                                            إعدادات الإشعارات
                                        </button>
                                        {% if current_user.role == 'admin' %}
                                        <a href="{{ url_for('admin.dashboard') }}" class="btn btn-success">
                                            <i class="fas fa-tachometer-alt me-1"></i>
                                            لوحة التحكم
                                        </a>
                                        {% elif current_user.role == 'inspector' %}
                                        <a href="{{ url_for('inspector.dashboard') }}" class="btn btn-success">
                                            <i class="fas fa-tachometer-alt me-1"></i>
                                            لوحة التحكم
                                        </a>
                                        {% elif current_user.role == 'teacher' %}
                                        <a href="{{ url_for('teacher.dashboard') }}" class="btn btn-success">
                                            <i class="fas fa-tachometer-alt me-1"></i>
                                            لوحة التحكم
                                        </a>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // يمكن إضافة JavaScript إضافي هنا للملف الشخصي
        console.log('تم تحميل صفحة الملف الشخصي');
    });
</script>
{% endblock %}
