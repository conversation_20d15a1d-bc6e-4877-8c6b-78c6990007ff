#!/usr/bin/env python3
"""
اختبار التطبيق بعد إصلاح مشكلة session.regenerate
"""

from app import create_app, db
from app.models import User
import requests
import time
import threading

def test_app():
    """اختبار التطبيق"""
    app = create_app()
    
    # تشغيل التطبيق في خيط منفصل
    def run_app():
        app.run(debug=False, use_reloader=False, port=5001)
    
    server_thread = threading.Thread(target=run_app, daemon=True)
    server_thread.start()
    
    # انتظار قليل لبدء الخادم
    time.sleep(2)
    
    try:
        # اختبار الصفحة الرئيسية
        print("اختبار الصفحة الرئيسية...")
        response = requests.get('http://127.0.0.1:5001/', timeout=5)
        if response.status_code == 200:
            print("✅ الصفحة الرئيسية تعمل بشكل صحيح")
        else:
            print(f"❌ خطأ في الصفحة الرئيسية: {response.status_code}")
        
        # اختبار صفحة تسجيل الدخول
        print("اختبار صفحة تسجيل الدخول...")
        response = requests.get('http://127.0.0.1:5001/auth/login', timeout=5)
        if response.status_code == 200:
            print("✅ صفحة تسجيل الدخول تعمل بشكل صحيح")
        else:
            print(f"❌ خطأ في صفحة تسجيل الدخول: {response.status_code}")
        
        print("\n🎉 جميع الاختبارات نجحت! التطبيق يعمل بشكل صحيح.")
        
    except requests.exceptions.RequestException as e:
        print(f"❌ خطأ في الاتصال: {e}")
        print("تأكد من أن التطبيق يعمل على المنفذ 5001")
    
    except Exception as e:
        print(f"❌ خطأ غير متوقع: {e}")

if __name__ == '__main__':
    print("=== اختبار التطبيق ===\n")
    test_app()
