{% extends 'base.html' %}

{% block title %}الإشعارات - Ta9affi{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-warning text-dark">
                    <h4 class="mb-0">
                        <i class="fas fa-bell me-2"></i>
                        الإشعارات بين المفتشين والأساتذة
                    </h4>
                </div>
                <div class="card-body">
                    {% if current_user.is_inspector() %}
                        <!-- واجهة المفتش -->
                        <div class="row">
                            <div class="col-md-4">
                                <div class="card border-0 bg-light">
                                    <div class="card-header bg-success text-white">
                                        <h5 class="mb-0">
                                            <i class="fas fa-paper-plane me-1"></i>
                                            إرسال إشعار للأساتذة
                                        </h5>
                                    </div>
                                    <div class="card-body">
                                        <form action="{{ url_for('inspector.send_notification_to_teachers') }}" method="post">
                                            <div class="mb-3">
                                                <label for="receiver_type" class="form-label">نوع الإرسال</label>
                                                <select class="form-select" id="receiver_type" name="receiver_type" required>
                                                    <option value="">اختر نوع الإرسال</option>
                                                    <option value="all">جميع الأساتذة تحت الإشراف</option>
                                                    <option value="specific">أستاذ محدد</option>
                                                </select>
                                            </div>
                                            <div class="mb-3" id="specific_teacher_div" style="display: none;">
                                                <label for="teacher_id" class="form-label">الأستاذ</label>
                                                <select class="form-select" id="teacher_id" name="teacher_id">
                                                    <option value="">اختر الأستاذ</option>
                                                    <!-- سيتم ملء هذه القائمة من قاعدة البيانات -->
                                                    <option value="1">أستاذ تجريبي</option>
                                                </select>
                                            </div>
                                            <div class="mb-3">
                                                <label for="title" class="form-label">العنوان</label>
                                                <input type="text" class="form-control" id="title" name="title" required>
                                            </div>
                                            <div class="mb-3">
                                                <label for="message" class="form-label">الرسالة</label>
                                                <textarea class="form-control" id="message" name="message" rows="4" required></textarea>
                                            </div>
                                            <div class="mb-3">
                                                <label for="priority" class="form-label">الأولوية</label>
                                                <select class="form-select" id="priority" name="priority">
                                                    <option value="normal">عادية</option>
                                                    <option value="high">عالية</option>
                                                    <option value="urgent">عاجلة</option>
                                                </select>
                                            </div>
                                            <div class="d-grid">
                                                <button type="submit" class="btn btn-success">
                                                    <i class="fas fa-send me-1"></i>
                                                    إرسال الإشعار
                                                </button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-8">
                                <div class="card border-0 bg-light">
                                    <div class="card-header bg-info text-white">
                                        <h5 class="mb-0">
                                            <i class="fas fa-history me-1"></i>
                                            الإشعارات المرسلة للأساتذة
                                        </h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="table-responsive">
                                            <table class="table table-striped table-hover">
                                                <thead class="table-light">
                                                    <tr>
                                                        <th>المستلم</th>
                                                        <th>العنوان</th>
                                                        <th>الأولوية</th>
                                                        <th>التاريخ</th>
                                                        <th>الحالة</th>
                                                        <th>الإجراءات</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <!-- سيتم ملء هذه البيانات من قاعدة البيانات -->
                                                    <tr>
                                                        <td colspan="6" class="text-center text-muted">
                                                            <i class="fas fa-info-circle me-1"></i>
                                                            لا توجد إشعارات مرسلة حتى الآن
                                                        </td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    {% elif current_user.is_teacher() %}
                        <!-- واجهة الأستاذ -->
                        <div class="row">
                            <div class="col-md-12">
                                <div class="card border-0 bg-light">
                                    <div class="card-header bg-primary text-white">
                                        <h5 class="mb-0">
                                            <i class="fas fa-inbox me-1"></i>
                                            الإشعارات الواردة من المفتشين
                                        </h5>
                                    </div>
                                    <div class="card-body">
                                        <!-- فلاتر الإشعارات -->
                                        <div class="row mb-3">
                                            <div class="col-md-4">
                                                <select class="form-select" id="priority_filter">
                                                    <option value="">جميع الأولويات</option>
                                                    <option value="normal">عادية</option>
                                                    <option value="high">عالية</option>
                                                    <option value="urgent">عاجلة</option>
                                                </select>
                                            </div>
                                            <div class="col-md-4">
                                                <select class="form-select" id="status_filter">
                                                    <option value="">جميع الحالات</option>
                                                    <option value="unread">غير مقروءة</option>
                                                    <option value="read">مقروءة</option>
                                                </select>
                                            </div>
                                            <div class="col-md-4">
                                                <button class="btn btn-outline-primary" onclick="filterNotifications()">
                                                    <i class="fas fa-filter me-1"></i>
                                                    تطبيق الفلتر
                                                </button>
                                            </div>
                                        </div>

                                        <div class="table-responsive">
                                            <table class="table table-striped table-hover">
                                                <thead class="table-light">
                                                    <tr>
                                                        <th>المرسل</th>
                                                        <th>العنوان</th>
                                                        <th>الأولوية</th>
                                                        <th>التاريخ</th>
                                                        <th>الحالة</th>
                                                        <th>الإجراءات</th>
                                                    </tr>
                                                </thead>
                                                <tbody id="notifications_table">
                                                    <!-- سيتم ملء هذه البيانات من قاعدة البيانات -->
                                                    <tr>
                                                        <td colspan="6" class="text-center text-muted">
                                                            <i class="fas fa-info-circle me-1"></i>
                                                            لا توجد إشعارات واردة حتى الآن
                                                        </td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </div>

                                        <!-- إحصائيات سريعة -->
                                        <div class="row mt-4">
                                            <div class="col-md-3">
                                                <div class="card border-0 bg-danger text-white">
                                                    <div class="card-body text-center">
                                                        <h5>0</h5>
                                                        <small>عاجلة</small>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="card border-0 bg-warning text-dark">
                                                    <div class="card-body text-center">
                                                        <h5>0</h5>
                                                        <small>عالية الأولوية</small>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="card border-0 bg-info text-white">
                                                    <div class="card-body text-center">
                                                        <h5>0</h5>
                                                        <small>غير مقروءة</small>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="card border-0 bg-success text-white">
                                                    <div class="card-body text-center">
                                                        <h5>0</h5>
                                                        <small>إجمالي الإشعارات</small>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal لعرض تفاصيل الإشعار -->
<div class="modal fade" id="notificationModal" tabindex="-1" aria-labelledby="notificationModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="notificationModalLabel">تفاصيل الإشعار</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div id="notificationContent">
                    <!-- سيتم ملء المحتوى بواسطة JavaScript -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                {% if current_user.is_teacher() %}
                <button type="button" class="btn btn-primary" id="markAsRead">تحديد كمقروء</button>
                <button type="button" class="btn btn-success" id="replyToNotification">رد على الإشعار</button>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // التعامل مع تغيير نوع الإرسال
        const receiverType = document.getElementById('receiver_type');
        const specificTeacherDiv = document.getElementById('specific_teacher_div');

        if (receiverType) {
            receiverType.addEventListener('change', function() {
                if (this.value === 'specific') {
                    specificTeacherDiv.style.display = 'block';
                    document.getElementById('teacher_id').required = true;
                } else {
                    specificTeacherDiv.style.display = 'none';
                    document.getElementById('teacher_id').required = false;
                }
            });
        }

        console.log('تم تحميل صفحة الإشعارات بين المفتشين والأساتذة');
    });

    // دالة تطبيق الفلتر
    function filterNotifications() {
        const priorityFilter = document.getElementById('priority_filter').value;
        const statusFilter = document.getElementById('status_filter').value;
        
        // هنا يمكن إضافة منطق الفلترة
        console.log('تطبيق الفلتر:', { priority: priorityFilter, status: statusFilter });
        
        // يمكن إرسال طلب AJAX لتحديث الجدول
        alert('ميزة الفلترة قيد التطوير');
    }
</script>
{% endblock %}
