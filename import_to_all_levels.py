from app_new import app, db, EducationalLevel, LevelDatabase, LevelDataEntry
import pandas as pd
import os

def import_data_to_level(level_id, excel_file):
    """استيراد البيانات من ملف Excel إلى مستوى تعليمي محدد"""
    
    # التحقق من وجود المستوى
    level = EducationalLevel.query.get(level_id)
    if not level:
        print(f"خطأ: المستوى التعليمي برقم {level_id} غير موجود")
        return False
    
    # التحقق من وجود قاعدة بيانات للمستوى
    database = LevelDatabase.query.filter_by(level_id=level_id).first()
    if not database:
        print(f"خطأ: لا توجد قاعدة بيانات للمستوى {level.name}")
        return False
    
    print(f"جاري استيراد البيانات إلى قاعدة بيانات {database.name}...")
    
    try:
        # قراءة ملف Excel
        df = pd.read_excel(excel_file, sheet_name=None)
        
        # حذف البيانات الحالية
        LevelDataEntry.query.filter_by(database_id=database.id).delete()
        db.session.commit()
        print(f"تم حذف البيانات الحالية من قاعدة بيانات {database.name}")
        
        # استيراد المواد الدراسية
        if 'Subjects' in df:
            subjects_df = df['Subjects']
            created_subjects = {}
            
            for _, row in subjects_df.iterrows():
                row_dict = row.to_dict()
                name = row_dict.get('name', '')
                description = row_dict.get('description', '')
                is_active = row_dict.get('is_active', True)
                
                if name:
                    subject = LevelDataEntry(
                        database_id=database.id,
                        entry_type='subject',
                        name=name,
                        description=description,
                        is_active=is_active
                    )
                    db.session.add(subject)
                    db.session.flush()  # للحصول على معرف العنصر
                    created_subjects[name] = subject.id
            
            print(f"تم استيراد {len(created_subjects)} مادة دراسية")
            
            # استيراد الميادين
            if 'Domains' in df:
                domains_df = df['Domains']
                created_domains = {}
                
                for _, row in domains_df.iterrows():
                    row_dict = row.to_dict()
                    name = row_dict.get('name', '')
                    description = row_dict.get('description', '')
                    parent_name = row_dict.get('parent_name', '')
                    is_active = row_dict.get('is_active', True)
                    
                    if name and parent_name in created_subjects:
                        domain = LevelDataEntry(
                            database_id=database.id,
                            entry_type='domain',
                            parent_id=created_subjects[parent_name],
                            name=name,
                            description=description,
                            is_active=is_active
                        )
                        db.session.add(domain)
                        db.session.flush()
                        created_domains[name] = domain.id
                
                print(f"تم استيراد {len(created_domains)} ميدان")
            
            db.session.commit()
            print(f"تم استيراد البيانات بنجاح إلى قاعدة بيانات {database.name}")
            return True
            
    except Exception as e:
        db.session.rollback()
        print(f"حدث خطأ أثناء استيراد البيانات: {str(e)}")
        return False

def main():
    """استيراد البيانات إلى جميع المستويات التعليمية"""
    with app.app_context():
        # الحصول على جميع المستويات التعليمية
        levels = EducationalLevel.query.all()
        
        if not levels:
            print("لا توجد مستويات تعليمية في قاعدة البيانات")
            return
        
        excel_file = 'educational_data.xlsx'
        if not os.path.exists(excel_file):
            print(f"خطأ: ملف {excel_file} غير موجود")
            return
        
        for level in levels:
            print(f"\n--- استيراد البيانات إلى المستوى: {level.name} ---")
            import_data_to_level(level.id, excel_file)

if __name__ == "__main__":
    main()
