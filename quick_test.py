#!/usr/bin/env python3
"""
اختبار سريع للتطبيق
"""

from app import create_app

def test_app_creation():
    """اختبار إنشاء التطبيق"""
    try:
        print("إنشاء التطبيق...")
        app = create_app()
        print("✅ تم إنشاء التطبيق بنجاح")
        
        with app.test_client() as client:
            print("اختبار الصفحة الرئيسية...")
            response = client.get('/')
            print(f"رمز الاستجابة: {response.status_code}")
            
            if response.status_code == 200:
                print("✅ الصفحة الرئيسية تعمل")
            else:
                print("❌ مشكلة في الصفحة الرئيسية")
            
            print("اختبار صفحة تسجيل الدخول...")
            response = client.get('/auth/login')
            print(f"رمز الاستجابة: {response.status_code}")
            
            if response.status_code == 200:
                print("✅ صفحة تسجيل الدخول تعمل")
            else:
                print("❌ مشكلة في صفحة تسجيل الدخول")
        
        print("\n🎉 جميع الاختبارات نجحت!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    print("=== اختبار سريع للتطبيق ===\n")
    success = test_app_creation()
    
    if success:
        print("\n✅ التطبيق جاهز للاستخدام!")
        print("يمكنك تشغيله باستخدام: python run.py")
    else:
        print("\n❌ هناك مشاكل تحتاج إلى إصلاح")
