from app import app, db
from models import User, Role, EducationalLevel, Subject, Domain, KnowledgeMaterial, Competency, LevelDatabase, LevelDataEntry
from werkzeug.security import generate_password_hash
from datetime import datetime
import os

def seed_users():
    """إضافة مستخدمين تجريبيين"""
    users = [
        # مستخدم إدارة
        {
            'username': 'admin',
            'email': '<EMAIL>',
            'password': generate_password_hash('admin123'),
            'role': Role.ADMIN
        },
        # مستخدم مفتش
        {
            'username': 'inspector',
            'email': '<EMAIL>',
            'password': generate_password_hash('inspector123'),
            'role': Role.INSPECTOR
        },
        # مستخدم أستاذ
        {
            'username': 'teacher',
            'email': '<EMAIL>',
            'password': generate_password_hash('teacher123'),
            'role': Role.TEACHER
        },
        # مستخدم أستاذ آخر
        {
            'username': 'teacher2',
            'email': '<EMAIL>',
            'password': generate_password_hash('teacher123'),
            'role': Role.TEACHER
        }
    ]

    for user_data in users:
        user = User.query.filter_by(username=user_data['username']).first()
        if not user:
            user = User(**user_data)
            db.session.add(user)

    db.session.commit()

    # إضافة علاقة بين المفتش والأساتذة
    inspector = User.query.filter_by(username='inspector').first()
    teacher1 = User.query.filter_by(username='teacher').first()
    teacher2 = User.query.filter_by(username='teacher2').first()

    if inspector and teacher1 and teacher2:
        inspector.supervised_teachers.append(teacher1)
        inspector.supervised_teachers.append(teacher2)
        db.session.commit()

def seed_educational_data():
    """إضافة بيانات تعليمية تجريبية"""

    # المستويات التعليمية
    levels = [
        {'name': 'السنة الأولى ابتدائي'},
        {'name': 'السنة الثانية ابتدائي'},
        {'name': 'السنة الثالثة ابتدائي'},
        {'name': 'السنة الرابعة ابتدائي'},
        {'name': 'السنة الخامسة ابتدائي'}
    ]

    for level_data in levels:
        level = EducationalLevel.query.filter_by(name=level_data['name']).first()
        if not level:
            level = EducationalLevel(**level_data)
            db.session.add(level)

    db.session.commit()

    # المواد الدراسية
    subjects = [
        {'name': 'اللغة العربية', 'level_id': 1},
        {'name': 'الرياضيات', 'level_id': 1},
        {'name': 'التربية العلمية', 'level_id': 1},
        {'name': 'التربية الإسلامية', 'level_id': 1},
        {'name': 'التربية المدنية', 'level_id': 1},

        {'name': 'اللغة العربية', 'level_id': 2},
        {'name': 'الرياضيات', 'level_id': 2},
        {'name': 'التربية العلمية', 'level_id': 2},
        {'name': 'التربية الإسلامية', 'level_id': 2},
        {'name': 'التربية المدنية', 'level_id': 2},

        {'name': 'اللغة العربية', 'level_id': 3},
        {'name': 'الرياضيات', 'level_id': 3},
        {'name': 'التربية العلمية', 'level_id': 3},
        {'name': 'التربية الإسلامية', 'level_id': 3},
        {'name': 'التربية المدنية', 'level_id': 3},

        {'name': 'اللغة العربية', 'level_id': 4},
        {'name': 'الرياضيات', 'level_id': 4},
        {'name': 'التربية العلمية', 'level_id': 4},
        {'name': 'التربية الإسلامية', 'level_id': 4},
        {'name': 'التربية المدنية', 'level_id': 4},

        {'name': 'اللغة العربية', 'level_id': 5},
        {'name': 'الرياضيات', 'level_id': 5},
        {'name': 'التربية العلمية', 'level_id': 5},
        {'name': 'التربية الإسلامية', 'level_id': 5},
        {'name': 'التربية المدنية', 'level_id': 5}
    ]

    for subject_data in subjects:
        # تجنب التكرار في حالة وجود المادة بالفعل
        subject = Subject.query.filter_by(name=subject_data['name'], level_id=subject_data['level_id']).first()
        if not subject:
            subject = Subject(**subject_data)
            db.session.add(subject)

    db.session.commit()

    # إضافة ميادين للغة العربية في السنة الأولى كمثال
    arabic_subject = Subject.query.filter_by(name='اللغة العربية', level_id=1).first()

    if arabic_subject:
        domains = [
            {'name': 'فهم المنطوق', 'subject_id': arabic_subject.id},
            {'name': 'التعبير الشفهي', 'subject_id': arabic_subject.id},
            {'name': 'فهم المكتوب', 'subject_id': arabic_subject.id},
            {'name': 'التعبير الكتابي', 'subject_id': arabic_subject.id}
        ]

        for domain_data in domains:
            domain = Domain.query.filter_by(name=domain_data['name'], subject_id=domain_data['subject_id']).first()
            if not domain:
                domain = Domain(**domain_data)
                db.session.add(domain)

        db.session.commit()

        # إضافة مواد معرفية لميدان فهم المكتوب كمثال
        reading_domain = Domain.query.filter_by(name='فهم المكتوب', subject_id=arabic_subject.id).first()

        if reading_domain:
            materials = [
                {'name': 'الحروف الهجائية', 'domain_id': reading_domain.id},
                {'name': 'الكلمات البصرية', 'domain_id': reading_domain.id},
                {'name': 'قراءة الجمل البسيطة', 'domain_id': reading_domain.id}
            ]

            for material_data in materials:
                material = KnowledgeMaterial.query.filter_by(name=material_data['name'], domain_id=material_data['domain_id']).first()
                if not material:
                    material = KnowledgeMaterial(**material_data)
                    db.session.add(material)

            db.session.commit()

            # إضافة كفاءات مستهدفة للحروف الهجائية كمثال
            letters_material = KnowledgeMaterial.query.filter_by(name='الحروف الهجائية', domain_id=reading_domain.id).first()

            if letters_material:
                competencies = [
                    {'description': 'يتعرف على الحروف الهجائية بأشكالها المختلفة', 'knowledge_material_id': letters_material.id},
                    {'description': 'يقرأ الحروف الهجائية بالحركات القصيرة', 'knowledge_material_id': letters_material.id},
                    {'description': 'يقرأ الحروف الهجائية بالمدود', 'knowledge_material_id': letters_material.id},
                    {'description': 'يكتب الحروف الهجائية بأشكالها المختلفة', 'knowledge_material_id': letters_material.id}
                ]

                for competency_data in competencies:
                    competency = Competency.query.filter_by(description=competency_data['description'], knowledge_material_id=competency_data['knowledge_material_id']).first()
                    if not competency:
                        competency = Competency(**competency_data)
                        db.session.add(competency)

                db.session.commit()

def seed_level_databases():
    """إضافة قواعد بيانات منفصلة للمستويات التعليمية"""
    # إنشاء مجلد البيانات إذا لم يكن موجوداً
    data_dir = os.path.join(app.root_path, 'data')
    os.makedirs(data_dir, exist_ok=True)

    # الحصول على المستويات التعليمية
    levels = EducationalLevel.query.all()

    for level in levels:
        # إنشاء قاعدة بيانات لكل مستوى
        db_name = f"level_{level.id}_db"
        file_path = f"data/{db_name}.sqlite"

        # التحقق من وجود قاعدة البيانات
        existing_db = LevelDatabase.query.filter_by(level_id=level.id).first()
        if not existing_db:
            new_db = LevelDatabase(
                level_id=level.id,
                name=f"قاعدة بيانات {level.name}",
                file_path=file_path,
                is_active=True
            )
            db.session.add(new_db)

    db.session.commit()

    # إضافة بيانات تجريبية لقاعدة بيانات المستوى الأول
    level1_db = LevelDatabase.query.filter_by(level_id=1).first()
    if level1_db:
        # إضافة مواد دراسية
        subjects = [
            {"name": "اللغة العربية", "description": "مادة اللغة العربية للسنة الأولى ابتدائي"},
            {"name": "الرياضيات", "description": "مادة الرياضيات للسنة الأولى ابتدائي"},
            {"name": "التربية العلمية", "description": "مادة التربية العلمية للسنة الأولى ابتدائي"}
        ]

        for subject_data in subjects:
            subject = LevelDataEntry(
                database_id=level1_db.id,
                entry_type="subject",
                name=subject_data["name"],
                description=subject_data["description"],
                is_active=True
            )
            db.session.add(subject)

        db.session.commit()

        # إضافة ميادين للغة العربية
        arabic_subject = LevelDataEntry.query.filter_by(database_id=level1_db.id, entry_type="subject", name="اللغة العربية").first()
        if arabic_subject:
            domains = [
                {"name": "فهم المنطوق", "description": "ميدان فهم المنطوق للسنة الأولى ابتدائي"},
                {"name": "التعبير الشفهي", "description": "ميدان التعبير الشفهي للسنة الأولى ابتدائي"},
                {"name": "فهم المكتوب", "description": "ميدان فهم المكتوب للسنة الأولى ابتدائي"},
                {"name": "التعبير الكتابي", "description": "ميدان التعبير الكتابي للسنة الأولى ابتدائي"}
            ]

            for domain_data in domains:
                domain = LevelDataEntry(
                    database_id=level1_db.id,
                    entry_type="domain",
                    parent_id=arabic_subject.id,
                    name=domain_data["name"],
                    description=domain_data["description"],
                    is_active=True
                )
                db.session.add(domain)

            db.session.commit()

            # إضافة مواد معرفية لميدان فهم المكتوب
            reading_domain = LevelDataEntry.query.filter_by(database_id=level1_db.id, entry_type="domain", name="فهم المكتوب").first()
            if reading_domain:
                materials = [
                    {"name": "الحروف الهجائية", "description": "تعلم الحروف الهجائية بأشكالها المختلفة"},
                    {"name": "الكلمات البصرية", "description": "التعرف على الكلمات البصرية الشائعة"},
                    {"name": "قراءة الجمل البسيطة", "description": "قراءة وفهم الجمل البسيطة"}
                ]

                for material_data in materials:
                    material = LevelDataEntry(
                        database_id=level1_db.id,
                        entry_type="material",
                        parent_id=reading_domain.id,
                        name=material_data["name"],
                        description=material_data["description"],
                        is_active=True
                    )
                    db.session.add(material)

                db.session.commit()

                # إضافة كفاءات مستهدفة للحروف الهجائية
                letters_material = LevelDataEntry.query.filter_by(database_id=level1_db.id, entry_type="material", name="الحروف الهجائية").first()
                if letters_material:
                    competencies = [
                        {"name": "التعرف على الحروف", "description": "يتعرف على الحروف الهجائية بأشكالها المختلفة"},
                        {"name": "قراءة الحروف", "description": "يقرأ الحروف الهجائية بالحركات القصيرة"},
                        {"name": "قراءة الحروف بالمدود", "description": "يقرأ الحروف الهجائية بالمدود"},
                        {"name": "كتابة الحروف", "description": "يكتب الحروف الهجائية بأشكالها المختلفة"}
                    ]

                    for competency_data in competencies:
                        competency = LevelDataEntry(
                            database_id=level1_db.id,
                            entry_type="competency",
                            parent_id=letters_material.id,
                            name=competency_data["name"],
                            description=competency_data["description"],
                            is_active=True
                        )
                        db.session.add(competency)

                    db.session.commit()

if __name__ == '__main__':
    with app.app_context():
        # إنشاء قاعدة البيانات إذا لم تكن موجودة
        db.create_all()

        # إضافة البيانات التجريبية
        seed_users()
        seed_educational_data()
        seed_level_databases()

        print("تم إضافة البيانات التجريبية بنجاح!")
