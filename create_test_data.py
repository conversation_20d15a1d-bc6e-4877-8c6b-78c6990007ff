from app_new import app, db, User, Role, ProgressEntry
from werkzeug.security import generate_password_hash
from datetime import datetime, timedelta
import random

def create_test_data():
    with app.app_context():
        # إنشاء مفتش للاختبار إذا لم يكن موجودًا
        inspector = User.query.filter_by(username='inspector_test').first()
        if not inspector:
            inspector = User(
                username='inspector_test',
                email='<EMAIL>',
                password=generate_password_hash('password'),
                role=Role.INSPECTOR
            )
            db.session.add(inspector)
            db.session.commit()
            print(f"تم إنشاء مفتش للاختبار: {inspector.username}")
        else:
            print(f"المفتش موجود بالفعل: {inspector.username}")

        # إنشاء أساتذة للاختبار
        teachers = []
        for i in range(1, 4):
            teacher = User.query.filter_by(username=f'teacher{i}_test').first()
            if not teacher:
                teacher = User(
                    username=f'teacher{i}_test',
                    email=f'teacher{i}@test.com',
                    password=generate_password_hash('password'),
                    role=Role.TEACHER
                )
                db.session.add(teacher)
                db.session.commit()
                print(f"تم إنشاء أستاذ للاختبار: {teacher.username}")
            else:
                print(f"الأستاذ موجود بالفعل: {teacher.username}")
            teachers.append(teacher)

        # ربط الأساتذة بالمفتش
        for teacher in teachers:
            if teacher not in inspector.supervised_teachers:
                inspector.supervised_teachers.append(teacher)
                print(f"تم ربط الأستاذ {teacher.username} بالمفتش {inspector.username}")
        db.session.commit()

        # إنشاء بيانات تقدم للأساتذة
        statuses = ['completed', 'in_progress', 'planned']
        for teacher in teachers:
            # حذف بيانات التقدم السابقة للأستاذ
            ProgressEntry.query.filter_by(user_id=teacher.id).delete()
            
            # إنشاء بيانات تقدم جديدة
            for i in range(10):
                status = random.choice(statuses)
                entry = ProgressEntry(
                    user_id=teacher.id,
                    competency_id=i + 1,  # رقم افتراضي للكفاءة
                    status=status,
                    date=(datetime.now() - timedelta(days=i)).date(),
                    notes=f"ملاحظة اختبار {i+1} للأستاذ {teacher.username}",
                    created_at=datetime.now() - timedelta(days=i),
                    updated_at=datetime.now() - timedelta(days=i)
                )
                db.session.add(entry)
            print(f"تم إنشاء بيانات تقدم للأستاذ {teacher.username}")
        
        db.session.commit()
        print("تم إنشاء بيانات الاختبار بنجاح")

if __name__ == "__main__":
    create_test_data()
