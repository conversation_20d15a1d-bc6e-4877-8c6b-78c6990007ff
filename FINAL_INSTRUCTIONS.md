# تعليمات استخدام التطبيق الجديد

## المشكلة التي تم حلها

تم إنشاء تطبيق جديد لحل مشكلة `sqlalchemy.exc.OperationalError: no such column: educational_level.is_active` وأي مشاكل أخرى متعلقة بهيكل قاعدة البيانات.

## الملفات الجديدة

1. **models_new.py**: نماذج قاعدة البيانات الجديدة
2. **app_new.py**: تطبيق Flask الجديد
3. **FINAL_INSTRUCTIONS.md**: هذا الملف (تعليمات الاستخدام)

## كيفية تشغيل التطبيق الجديد

### 1. تثبيت المتطلبات

تأكد من تثبيت جميع المتطلبات:
```
pip install flask flask-sqlalchemy flask-login flask-wtf werkzeug pandas openpyxl
```

### 2. تشغيل التطبيق الجديد

قم بتشغيل التطبيق الجديد باستخدام الأمر التالي:
```
python app_new.py
```

هذا سيقوم بما يلي:
- إنشاء قاعدة بيانات جديدة باسم `ta9affi_new.db`
- إنشاء جميع الجداول بالهيكل الصحيح
- إضافة البيانات التجريبية (المستخدمين، المستويات، قواعد البيانات المنفصلة)

### 3. الوصول إلى التطبيق

افتح المتصفح وانتقل إلى:
```
http://127.0.0.1:5000
```

### 4. تسجيل الدخول

قم بتسجيل الدخول باستخدام حساب الإدارة:
- اسم المستخدم: `admin`
- كلمة المرور: `admin123`

### 5. الوصول إلى إدارة قواعد البيانات المنفصلة

بعد تسجيل الدخول، انتقل إلى:
```
http://127.0.0.1:5000/admin/databases
```

## المسارات المتاحة

### المسارات العامة
- `/`: الصفحة الرئيسية
- `/login`: تسجيل الدخول
- `/register`: إنشاء حساب جديد
- `/logout`: تسجيل الخروج

### لوحات التحكم
- `/dashboard`: لوحة التحكم الرئيسية (توجيه حسب الدور)
- `/dashboard/admin`: لوحة تحكم الإدارة
- `/dashboard/inspector`: لوحة تحكم المفتش
- `/dashboard/teacher`: لوحة تحكم الأستاذ

### إدارة قواعد البيانات المنفصلة
- `/admin/databases`: عرض قواعد البيانات المنفصلة
- `/admin/databases/add`: إضافة قاعدة بيانات جديدة
- `/admin/databases/<db_id>/view`: عرض محتوى قاعدة البيانات
- `/admin/databases/<db_id>/edit`: تعديل قاعدة البيانات
- `/admin/databases/<db_id>/delete`: حذف قاعدة البيانات
- `/admin/databases/<db_id>/toggle/<action>`: تفعيل/تعطيل قاعدة البيانات

### إدارة عناصر قاعدة البيانات
- `/admin/databases/<db_id>/entries/add`: إضافة عنصر جديد
- `/admin/databases/<db_id>/entries/<entry_id>/edit`: تعديل عنصر
- `/admin/databases/<db_id>/entries/<entry_id>/delete`: حذف عنصر

### البرنامج السنوي للتدريس
- `/teaching-program`: عرض البرنامج السنوي للتدريس
- `/progress/add`: إضافة تقدم جديد

### إدارة جدول التدريس
- `/schedule/manage`: إدارة جدول التدريس
- `/schedule/add`: إضافة حصة جديدة
- `/schedule/delete/<schedule_id>`: حذف حصة

### واجهات API للقوائم المنسدلة المعتمدة
- `/api/subjects/<level_id>`: الحصول على المواد الدراسية لمستوى معين
- `/api/domains/<subject_id>`: الحصول على الميادين لمادة معينة
- `/api/knowledge-materials/<domain_id>`: الحصول على المواد المعرفية لميدان معين
- `/api/competencies/<material_id>`: الحصول على الكفاءات المستهدفة لمادة معرفية معينة

### تصدير واستيراد البيانات
- `/data/export/<model_name>`: تصدير بيانات نموذج معين إلى ملف Excel
- `/data/import/<model_name>`: استيراد بيانات نموذج معين من ملف Excel
- `/admin/databases/<db_id>/export`: تصدير بيانات قاعدة بيانات معينة إلى ملف Excel
- `/admin/databases/<db_id>/import`: استيراد بيانات إلى قاعدة بيانات معينة من ملف Excel

## حسابات المستخدمين التجريبية

### حساب الإدارة
- اسم المستخدم: `admin`
- كلمة المرور: `admin123`

### حساب المفتش
- اسم المستخدم: `inspector`
- كلمة المرور: `inspector123`

### حساب الأستاذ
- اسم المستخدم: `teacher`
- كلمة المرور: `teacher123`

### حساب أستاذ آخر
- اسم المستخدم: `teacher2`
- كلمة المرور: `teacher123`

## ملاحظات هامة

1. هذا تطبيق جديد تمامًا يستخدم قاعدة بيانات مختلفة عن التطبيق القديم.
2. تم تصميم النماذج بعناية لتجنب أي مشاكل مع أسماء الأعمدة المحجوزة.
3. يستخدم التطبيق الجديد نفس القوالب من التطبيق القديم.
4. إذا واجهت أي مشاكل مع القوالب، تأكد من نسخها من التطبيق القديم إلى المجلد الصحيح.

## الخطوات التالية

1. **نقل القوالب**: تأكد من نسخ جميع القوالب من التطبيق القديم إلى مجلد `templates` في التطبيق الجديد.
2. **نقل الملفات الثابتة**: انسخ مجلد `static` من التطبيق القديم إلى التطبيق الجديد.
3. **تخصيص التطبيق**: قم بتخصيص التطبيق حسب احتياجاتك.
