import click
from flask.cli import with_appcontext
from app.models import User, Role
from app import db

@click.command('init-default-users')
@with_appcontext
def init_default_users():
    """إنشاء المستخدمين الافتراضيين إذا لم يكونوا موجودين"""
    # التحقق من وجود مستخدم مدير
    admin = User.query.filter_by(username='admin').first()
    if not admin:
        admin = User(username='admin', email='<EMAIL>', role='admin')
        admin.set_password('Admin@1234')
        db.session.add(admin)
        click.echo('تم إنشاء حساب المدير الافتراضي')
    
    # التحقق من وجود مستخدم مفتش
    inspector = User.query.filter_by(username='inspector').first()
    if not inspector:
        inspector = User(username='inspector', email='<EMAIL>', role='inspector')
        inspector.set_password('Inspector#2024')
        db.session.add(inspector)
        click.echo('تم إنشاء حساب المفتش الافتراضي')
    
    # التحقق من وجود مستخدم أستاذ
    teacher = User.query.filter_by(username='teacher').first()
    if not teacher:
        teacher = User(username='teacher', email='<EMAIL>', role='teacher')
        teacher.set_password('Teacher@2024')
        db.session.add(teacher)
        click.echo('تم إنشاء حساب الأستاذ الافتراضي')
    
    db.session.commit()
    click.echo('تم إنشاء جميع المستخدمين الافتراضيين بنجاح')