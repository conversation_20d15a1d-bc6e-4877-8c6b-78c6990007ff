"""مسارات المفتش لتطبيق Ta9affi"""

from flask import Blueprint, render_template, redirect, url_for, flash, request, jsonify
from flask_login import login_required, current_user
from app.models import User, Role, EducationalLevel, Subject, Domain, KnowledgeMaterial, Competency, InspectorTeacherNotification
from app import db
from app.utils.decorators import inspector_required
from datetime import datetime

# إنشاء بلوبرنت للمفتش
inspector_bp = Blueprint('inspector', __name__)

@inspector_bp.route('/inspector/dashboard')
@login_required
@inspector_required
def dashboard():
    """
    لوحة تحكم المفتش
    """
    # إحصائيات للوحة التحكم
    teachers_count = current_user.supervised_teachers.count()
    levels_count = EducationalLevel.query.count()
    subjects_count = Subject.query.count()
    
    # قائمة الأساتذة الذين يشرف عليهم المفتش
    teachers = current_user.supervised_teachers.all()
    
    return render_template('inspector/dashboard.html',
                           teachers_count=teachers_count,
                           levels_count=levels_count,
                           subjects_count=subjects_count,
                           teachers=teachers)

@inspector_bp.route('/inspector/manage_teachers')
@login_required
@inspector_required
def manage_teachers():
    """
    إدارة الأساتذة
    """
    # جلب جميع الأساتذة الذين يشرف عليهم المفتش
    teachers = current_user.supervised_teachers.all()
    
    # جلب جميع الأساتذة غير المرتبطين بالمفتش
    unassigned_teachers = User.query.filter_by(role=Role.TEACHER)\
        .filter(~User.id.in_([t.id for t in teachers]))\
        .all()
    
    return render_template('inspector/manage_teachers.html',
                           teachers=teachers,
                           unassigned_teachers=unassigned_teachers)

@inspector_bp.route('/inspector/assign_teacher/<int:teacher_id>', methods=['POST'])
@login_required
@inspector_required
def assign_teacher(teacher_id):
    """
    إضافة أستاذ إلى قائمة الإشراف
    """
    teacher = User.query.get_or_404(teacher_id)
    
    # التحقق من أن المستخدم هو أستاذ
    if not teacher.is_teacher():
        flash('المستخدم المحدد ليس أستاذاً.', 'danger')
        return redirect(url_for('inspector.manage_teachers'))
    
    # التحقق من أن الأستاذ غير مرتبط بالفعل بالمفتش
    if teacher in current_user.supervised_teachers.all():
        flash('الأستاذ مرتبط بالفعل بك.', 'warning')
        return redirect(url_for('inspector.manage_teachers'))
    
    # إضافة الأستاذ إلى قائمة الإشراف
    current_user.supervised_teachers.append(teacher)
    db.session.commit()
    
    # إرسال إشعار للأستاذ
    notification = InspectorTeacherNotification(
        sender_id=current_user.id,
        receiver_id=teacher.id,
        title='تم تعيين مفتش جديد',
        message=f'تم تعيين المفتش {current_user.username} للإشراف على تقدمك.'
    )
    db.session.add(notification)
    db.session.commit()
    
    flash(f'تم إضافة الأستاذ {teacher.username} إلى قائمة الإشراف بنجاح!', 'success')
    return redirect(url_for('inspector.manage_teachers'))

@inspector_bp.route('/inspector/unassign_teacher/<int:teacher_id>', methods=['POST'])
@login_required
@inspector_required
def unassign_teacher(teacher_id):
    """
    إزالة أستاذ من قائمة الإشراف
    """
    teacher = User.query.get_or_404(teacher_id)
    
    # التحقق من أن الأستاذ مرتبط بالمفتش
    if teacher not in current_user.supervised_teachers.all():
        flash('الأستاذ غير مرتبط بك.', 'warning')
        return redirect(url_for('inspector.manage_teachers'))
    
    # إزالة الأستاذ من قائمة الإشراف
    current_user.supervised_teachers.remove(teacher)
    db.session.commit()
    
    # إرسال إشعار للأستاذ
    notification = InspectorTeacherNotification(
        sender_id=current_user.id,
        receiver_id=teacher.id,
        title='تم إزالة المفتش',
        message=f'تم إزالة المفتش {current_user.username} من الإشراف على تقدمك.'
    )
    db.session.add(notification)
    db.session.commit()
    
    flash(f'تم إزالة الأستاذ {teacher.username} من قائمة الإشراف بنجاح!', 'success')
    return redirect(url_for('inspector.manage_teachers'))

@inspector_bp.route('/inspector/view_teacher_progress/<int:teacher_id>')
@login_required
@inspector_required
def view_teacher_progress(teacher_id):
    """
    عرض تقدم الأستاذ
    """
    teacher = User.query.get_or_404(teacher_id)
    
    # التحقق من أن الأستاذ مرتبط بالمفتش
    if teacher not in current_user.supervised_teachers.all():
        flash('غير مصرح لك بعرض تقدم هذا الأستاذ.', 'danger')
        return redirect(url_for('inspector.manage_teachers'))
    
    # جلب بيانات تقدم الأستاذ
    progress_entries = teacher.progress_entries
    
    # حساب إحصائيات التقدم
    completed_count = sum(1 for entry in progress_entries if entry.status == 'completed')
    in_progress_count = sum(1 for entry in progress_entries if entry.status == 'in_progress')
    planned_count = sum(1 for entry in progress_entries if entry.status == 'planned')
    total_count = len(progress_entries)
    
    # حساب نسبة الإكمال
    completion_rate = (completed_count / total_count * 100) if total_count > 0 else 0
    
    return render_template('inspector/view_teacher_progress.html',
                           teacher=teacher,
                           progress_entries=progress_entries,
                           completed_count=completed_count,
                           in_progress_count=in_progress_count,
                           planned_count=planned_count,
                           total_count=total_count,
                           completion_rate=completion_rate)

@inspector_bp.route('/inspector/send_notification/<int:teacher_id>', methods=['GET', 'POST'])
@login_required
@inspector_required
def send_notification(teacher_id):
    """
    إرسال إشعار لأستاذ
    """
    teacher = User.query.get_or_404(teacher_id)
    
    # التحقق من أن الأستاذ مرتبط بالمفتش
    if teacher not in current_user.supervised_teachers.all():
        flash('غير مصرح لك بإرسال إشعارات لهذا الأستاذ.', 'danger')
        return redirect(url_for('inspector.manage_teachers'))
    
    if request.method == 'POST':
        title = request.form.get('title')
        message = request.form.get('message')
        
        # إنشاء إشعار جديد
        notification = InspectorTeacherNotification(
            sender_id=current_user.id,
            receiver_id=teacher.id,
            title=title,
            message=message
        )
        db.session.add(notification)
        db.session.commit()
        
        flash(f'تم إرسال الإشعار إلى الأستاذ {teacher.username} بنجاح!', 'success')
        return redirect(url_for('inspector.manage_teachers'))
    
    return render_template('inspector/send_notification.html', teacher=teacher)

@inspector_bp.route('/inspector/view_notifications')
@login_required
@inspector_required
def view_notifications():
    """
    عرض الإشعارات الواردة للمفتش
    """
    # جلب الإشعارات الواردة من الإدارة
    notifications = current_user.received_admin_inspector_notifications
    
    # تحديث حالة الإشعارات غير المقروءة
    for notification in notifications:
        if not notification.is_read:
            notification.is_read = True
    
    db.session.commit()
    
    return render_template('inspector/view_notifications.html', notifications=notifications)