from app_new import app, db, LevelDataEntry
import json

def add_materials_to_domains(level_db_id, materials_data):
    """
    إضافة موارد معرفية متعددة إلى ميادين متعددة
    
    المعلمات:
    level_db_id: معرف قاعدة بيانات المستوى
    materials_data: قاموس يحتوي على بيانات المواد والميادين والموارد المعرفية
    """
    with app.app_context():
        try:
            total_added = 0
            
            for subject_name, domains_data in materials_data.items():
                # البحث عن المادة الدراسية
                subject = LevelDataEntry.query.filter_by(
                    database_id=level_db_id,
                    entry_type='subject',
                    name=subject_name
                ).first()
                
                if not subject:
                    print(f"خطأ: لم يتم العثور على المادة '{subject_name}' في قاعدة البيانات {level_db_id}")
                    continue
                
                for domain_name, materials_list in domains_data.items():
                    # البحث عن الميدان
                    domain = LevelDataEntry.query.filter_by(
                        database_id=level_db_id,
                        entry_type='domain',
                        parent_id=subject.id,
                        name=domain_name
                    ).first()
                    
                    if not domain:
                        print(f"خطأ: لم يتم العثور على الميدان '{domain_name}' في المادة '{subject_name}'")
                        continue
                    
                    # إضافة الموارد المعرفية
                    added_count = 0
                    for material_name in materials_list:
                        if not material_name or material_name.strip() == "":
                            continue
                            
                        # التحقق من وجود المورد المعرفي
                        existing_material = LevelDataEntry.query.filter_by(
                            database_id=level_db_id,
                            entry_type='material',
                            parent_id=domain.id,
                            name=material_name
                        ).first()
                        
                        if existing_material:
                            print(f"المورد المعرفي '{material_name}' موجود بالفعل في الميدان '{domain_name}'")
                            continue
                        
                        # إضافة المورد المعرفي
                        material = LevelDataEntry(
                            database_id=level_db_id,
                            entry_type='material',
                            parent_id=domain.id,
                            name=material_name,
                            description=f"مورد معرفي في ميدان {domain_name}",
                            is_active=True
                        )
                        db.session.add(material)
                        added_count += 1
                    
                    total_added += added_count
                    print(f"تم إضافة {added_count} مورد معرفي إلى الميدان '{domain_name}' في المادة '{subject_name}'")
            
            db.session.commit()
            print(f"تم إضافة {total_added} مورد معرفي بنجاح")
            return True
            
        except Exception as e:
            db.session.rollback()
            print(f"حدث خطأ أثناء إضافة الموارد المعرفية: {str(e)}")
            return False

def load_materials_from_file(file_path):
    """
    تحميل بيانات الموارد المعرفية من ملف JSON
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            return json.load(file)
    except Exception as e:
        print(f"خطأ في قراءة ملف البيانات: {str(e)}")
        return None

def save_materials_to_file(file_path, materials_data):
    """
    حفظ بيانات الموارد المعرفية إلى ملف JSON
    """
    try:
        with open(file_path, 'w', encoding='utf-8') as file:
            json.dump(materials_data, file, ensure_ascii=False, indent=4)
        print(f"تم حفظ البيانات بنجاح إلى الملف: {file_path}")
        return True
    except Exception as e:
        print(f"خطأ في حفظ البيانات إلى ملف: {str(e)}")
        return False

# بيانات الموارد المعرفية للسنة الرابعة ابتدائي
materials_data = {
    "اللغة العربية": {
        "فهم المنطوق": [
            "صديقتي حورية",
            "البائع الصغير",
            "جدي",
            "جيران الأمس و اليوم",
            "رامي و المعلم الجديد",
            "الجار الجديد",
            "بعيدا عن أرضي",
            "الأمير عبد القادر",
            "مطار مصطفى بن بولعيد",
            "نظافة المدرسة",
            "العيش في المدينة",
            "المسكن الشمسي",
            "هدية النخلة",
            "معاناة مريض",
            "أكتشف اللعبة",
            "أنامل معطرة",
            "البرنوس",
            "الطاسيلي ناجر",
            "الغواصة الاِستكشافية",
            "من عصر الحجارة إلى عصر الحاسوب",
            "القلم عبر التاريخ",
            "جمال بلادي",
            "صحراءنا الجميلة"
        ],
        "فهم المكتوب (ألعاب قرائية)": [
            "مع عصاي في المدرسة",
            "ماسح الزجاج",
            "حفنة نقود",
            "التاجماعث",
            "المعلم الجديد",
            "بين جارين",
            "الحنين إلى الوطن",
            "الأمير عبد القادر",
            "الزائر العزيز",
            "رسالة الثعلب",
            "بيوتنا بين الأمس واليوم",
            "طاقة لا تنفذ",
            "قصة زيتونة",
            "مرض سامية",
            "لمن تهتف الحناجر",
            "أنامل من ذهب",
            "لباسنا الجميل",
            "القاص الطارقي",
            "مركبة الأعماق",
            "سالم والحاسوب",
            "بهية والقلم",
            "جولة في بلادي",
            "حكايات في حقيبتي"
        ],
        "التعبير الشفهي": [
            "صديقتي حورية",
            "البائع الصغير",
            "جدي",
            "جيران الأمس و اليوم",
            "رامي و المعلم الجديد",
            "الجار الجديد",
            "بعيدا عن أرضي",
            "الأمير عبد القادر",
            "مطار مصطفى بن بولعيد",
            "نظافة المدرسة",
            "العيش في المدينة",
            "المسكن الشمسي",
            "هدية النخلة",
            "معاناة مريض",
            "أكتشف اللعبة",
            "أنامل معطرة",
            "البرنوس",
            "الطاسيلي ناجر",
            "الغواصة الاِستكشافية",
            "من عصر الحجارة إلى عصر الحاسوب",
            "القلم عبر التاريخ",
            "جمال بلادي",
            "صحراءنا الجميلة"
        ]
    }
}

# حفظ البيانات إلى ملف JSON
save_materials_to_file('materials_data.json', materials_data)

if __name__ == "__main__":
    # إضافة الموارد المعرفية إلى ميادين مادة "اللغة العربية" للسنة الرابعة ابتدائي
    add_materials_to_domains(4, materials_data)
