from app_new import app, db, LevelDataEntry

def main():
    with app.app_context():
        # إضافة ميدان "حفظ سورة/آية" لمادة "حفظ القرآن" في جميع المستويات
        for db_id in range(1, 6):  # المستويات من 1 إلى 5
            # البحث عن مادة حفظ القرآن
            quran = LevelDataEntry.query.filter_by(database_id=db_id, entry_type='subject', name='حفظ القرآن').first()
            
            if quran:
                # التحقق من وجود الميدان
                domain = LevelDataEntry.query.filter_by(
                    database_id=db_id,
                    entry_type='domain',
                    parent_id=quran.id,
                    name='حفظ سورة/آية'
                ).first()
                
                if not domain:
                    # إضافة الميدان
                    domain = LevelDataEntry(
                        database_id=db_id,
                        entry_type='domain',
                        parent_id=quran.id,
                        name='حفظ سورة/آية',
                        description='ميدان في حفظ القرآن',
                        is_active=True
                    )
                    db.session.add(domain)
                    print(f"تمت إضافة ميدان 'حفظ سورة/آية' لمادة 'حفظ القرآن' في قاعدة البيانات {db_id}")
                else:
                    print(f"ميدان 'حفظ سورة/آية' موجود بالفعل في قاعدة البيانات {db_id}")
            else:
                print(f"لم يتم العثور على مادة 'حفظ القرآن' في قاعدة البيانات {db_id}")
        
        db.session.commit()
        print("تم الانتهاء من إضافة الميادين")

if __name__ == "__main__":
    main()
