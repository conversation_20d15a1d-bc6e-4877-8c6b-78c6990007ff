# متطلبات تطبيق Ta9affi

# Flask وملحقاته
Flask==2.2.3
Flask-Login==0.6.2
Flask-SQLAlchemy==3.0.3
Flask-WTF==1.1.1
WTForms==3.0.1
Werkzeug==2.2.3

# قاعدة البيانات
SQLAlchemy==2.0.7

# معالجة البيانات
pandas==1.5.3
openpyxl==3.1.2
xlrd==2.0.1

# أدوات مساعدة
python-dotenv==1.0.0
email-validator==2.0.0

# الأمان
passlib==1.7.4
pyOpenSSL==23.0.0
flask-talisman==1.0.0  # لإضافة رؤوس HTTP الأمنية
flask-limiter==3.3.1  # للحماية من هجمات القوة الغاشمة
